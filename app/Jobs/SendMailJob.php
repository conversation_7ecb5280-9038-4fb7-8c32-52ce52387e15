<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class Send<PERSON>ailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $toEmail;
    protected $mailable;
    protected $mailer;

    /**
     * Create a new job instance.
     */
    public function __construct($toEmail, Mailable $mailable, $mailer = null)
    {
        $this->toEmail = $toEmail;
        $this->mailable = $mailable;
        $this->mailer = $mailer;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $mail = $this->mailer ? Mail::mailer($this->mailer) : Mail::to($this->toEmail);
        $mail->send($this->mailable);
    }
}

