<?php

namespace App\Jobs;

use App\Enums\ServerExternalStatus;
use App\Helpers\MailHelper;
use App\Mail\ServerDownMail;
use App\Models\Server;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;

class CheckServerUpdatesJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /**
         * Get all servers
         */
        $servers = Server::all();

        $fiveMinutesAgo = Carbon::now()->subMinutes(5);

        foreach ($servers as $server) {

            $shouldReport = !$server['last_connection_at'] || Carbon::make($server['last_connection_at'])->lt($fiveMinutesAgo);

            /**
             * Check if the server has connected in the last 5 minutes
             */
            if ($server->isUp() && $shouldReport) {
                /**
                 * Mark the server as down
                 */
                $server->update([
                    'external_status' => ServerExternalStatus::DOWN->value
                ]);

                MailHelper::queue('<EMAIL>', new ServerDownMail($server));
            } else if ($server->isDown() && !$shouldReport) {
                /**
                 * Mark the server as up
                 */
                $server->update([
                    'external_status' => ServerExternalStatus::UP->value
                ]);
            }
        }
    }
}
