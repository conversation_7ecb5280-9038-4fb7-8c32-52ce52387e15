<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ServerDownMail extends Mailable
{
    use Queueable, SerializesModels;

    public $server;

    /**
     * Create a new $server instance.
     */
    public function __construct($server)
    {
        $this->server = $server;
    }

    public function build()
    {
        return $this->from(config('mail.mailers.alarm.username'), 'Server Alerts')
            ->subject("Server Down Alert: {$this->server->name}")
            ->markdown('mails.server_down')
            ->with([
                'serverName' => $this->server->name,
                'ipAddress' => $this->server->ip,
                'checkedAt' => now()->toDateTimeString(),
            ]);
    }
}
