<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\Package;
use App\Enums\PackageStatusEnum;

class IsPackageAvailable implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $package = Package::find($value);
        
        if (!$package || $package->status !== PackageStatusEnum::Available->value) {
            $fail(__('validation.package_not_available'));
        }

        if ($package->subscriptions()->where('client_id', auth()->id())->active()->exists()) {
            $fail(__('validation.package_already_subscribed'));
        }
    }
}
