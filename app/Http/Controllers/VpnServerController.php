<?php

namespace App\Http\Controllers;

use App\Enums\PortStatusEnum;
use App\Http\Requests\VpnRequests\UpdateConnectionsCountRequest;
use App\Http\Requests\VpnRequests\VpnServerRequest;
use App\Services\VpnServerService;

class VpnServerController extends Controller
{
    protected $service;

    public function __construct()
    {
        $this->service = new VpnServerService();   
    }

    public function updateResourcesStatus(VpnServerRequest $request)
    {
        $this->service->updateResourcesStatus($request);
        return self::jsonResponse('success');
    }

    public function updateConnectionsCount(UpdateConnectionsCountRequest $request)
    {
       $this->service->updateConnectionsCount($request);
        return self::jsonResponse('success');
    }
}
