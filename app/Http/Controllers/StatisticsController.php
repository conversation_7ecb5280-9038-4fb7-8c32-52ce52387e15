<?php

namespace App\Http\Controllers;
use App\Services\StatisticsServices\ClientService;

class StatisticsController extends Controller
{
    protected $clientService;

    function __construct()
    {
        $this->middleware('permission:statistics/clients', ['only' => ['clientsStatistics']]);
        $this->clientService = new ClientService();
    }

    /**
     * Get statistics for clients/users
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function clientsStatistics()
    {
        $statistics = [
            'total_users' => $this->clientService->getTotalUsers(),
            'free_users' => $this->clientService->getFreeUsers(),
            'paid_users' => $this->clientService->getPaidUsers(),
            'logged_in_users' => $this->clientService->getLoggedInUsers(),
            'new_users' => $this->clientService->getNewUsers(),
            'user_distribution' => $this->clientService->getUserDistributionByCountry(),
            'conversion_rate' => $this->clientService->getConversionRate(),
        ];

        return self::jsonResponse('success', $statistics);
    }

}
