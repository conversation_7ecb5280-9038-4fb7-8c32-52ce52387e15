<?php

namespace App\Http\Controllers\MobileControllers;


use App\Http\Controllers\Controller;
use App\Http\Resources\MobileResources\SettingResource;
use App\Models\Setting;
use Illuminate\Http\Response;

class SettingController extends Controller
{
    public function index()
    {
        return self::MobileResponse('success', SettingResource::collection(Setting::get()));
    }

    /**
     * @urlParam code string required The Code of the Setting.Example: conn_limit
     */
    public function show($code)
    {
        $setting = Setting::where('code', $code)->firstOrFail();
        return self::MobileResponse('success', SettingResource::make($setting));
    }
}
