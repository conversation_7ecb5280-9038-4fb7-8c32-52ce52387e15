<?php

namespace App\Http\Controllers\MobileControllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\MobileRequests\TicketRequests\StoreTicketRequest;
use App\Models\Ticket;
use Illuminate\Http\Request;

class TicketController extends Controller
{

    /**
     */

    public function store(StoreTicketRequest $request)
    {
        $ticket = Ticket::create($request->validated());
        return self::MobileResponse('success', []);
    }


}
