<?php

namespace App\Http\Controllers\MobileControllers\Auth;

use App\Enums\ClientTypesEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\MobileRequests\SocialLoginRequest;
use App\Models\Client;
use Illuminate\Http\Request;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;

class SocialLoginController extends Controller
{


    public function register(SocialLoginRequest $request)
    {
        $provider = $request->provider;
        $token = $request->token;

        $user = Socialite::driver($provider)->stateless()->userFromToken($token);
        $client = $this->storeClient($provider, $user);
        auth()->login($client);
        $user = auth()->user();
        $res = $user->tokenize();
        return self::jsonResponse('success', $res);
    }

    protected function storeClient($provider, $user)
    {
        $client = Client::updateOrCreate([
            'email' => $user->getEmail(),
        ], [
            'username' => explode('@', $user->getEmail())[0],
            'type' => ClientTypesEnum::FREE->value,
        ]);
        
        $client->update(['username' => $client->id . Str::random(3) ]);

        $client->ExternalLogins()->updateOrCreate([
            'provider' => $provider,
            'provider_id' => $user->getId()
        ]);
        return $client;
    }
}
