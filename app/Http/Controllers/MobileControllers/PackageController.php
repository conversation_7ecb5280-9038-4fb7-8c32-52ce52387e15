<?php

namespace App\Http\Controllers\MobileControllers;

use App\Enums\SubscriptionStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\MobileRequests\PackagesRequest;
use App\Http\Requests\MobileRequests\PackagesSubscribeRequest;
use App\Http\Resources\MobileResources\PackageResource;
use App\Models\Package;


class PackageController extends Controller
{

    /**
     * @bodyParam limit integer The number of items per page. Example: 10
     * @bodyParam page integer The page number. Example: 1
     */
    public function packages(PackagesRequest $request)
    {
        $packages = Package::query()->paginate($request->limit, '*','page', $request->page);

        return self::MobileResponse('success', PackageResource::collection($packages));
    }

    public function subscribe(PackagesSubscribeRequest $request)
    {
        $package = Package::find($request->package_id);
        $package->subscriptions()->store($package);
        return self::MobileResponse('success', null);
    }

        

}
