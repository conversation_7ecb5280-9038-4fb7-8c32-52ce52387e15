<?php

namespace App\Http\Controllers\MainControllers;

use App\Enums\ClientStatusEnum;
use App\Enums\ClientTypesEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\ClientRequests\IndexClientRequest;
use App\Http\Requests\GroupRequests\DetachClientRequest;
use App\Http\Requests\GroupRequests\GroupClientsRequest;
use App\Http\Requests\GroupRequests\IndexGroupRequest;
use App\Http\Requests\GroupRequests\StoreGroupRequest;
use App\Http\Requests\GroupRequests\UpdateGroupRequest;
use App\Http\Resources\ClientResource;
use App\Http\Resources\GroupResource;
use App\Models\Client;
use App\Models\Group;
use App\Services\GroupService;
use Illuminate\Validation\ValidationException;

class GroupController extends Controller
{

    protected $service;
    function __construct()
    {
        $this->middleware('permission:groups', ['only' => ['index']]);
        $this->middleware('permission:groups/create', ['only' => ['store']]);
        $this->middleware('permission:groups/update', ['only' => ['update']]);
        $this->middleware('permission:groups/details', ['only' => ['show']]);
        $this->middleware('permission:groups/delete', ['only' => ['destroy']]);
        $this->service = new GroupService();
    }

    /**
     * Display a listing of the resource.
     */
    public function index(IndexGroupRequest $request)
    {
        $query = Group::search($request->search)
        ->query(function($query) use ($request){
            $query = $this->orderBy($query, $request, 'groups');
        });
        return GroupResource::collection($query->paginate($request->limit, 'page', $request->page));
    }

    /**
     * @param StoreGroupRequest $request
     * @return false|string
     */
    public function store(StoreGroupRequest $request)
    {
        $this->service->store($request);
        return self::jsonResponse('success', null);
    }

    /***
     * @param Group $group
     * @return false|string
     */
    public function show(Group $group)
    {
        $group->load('clients');
        $group->clients_ids = $group->clients->pluck('id')->toArray();
        return self::jsonResponse('success', $group);
    }

    /**
     * @param UpdateGroupRequest $request
     * @param Group $group
     * @return false|string
     */
    public function update(UpdateGroupRequest $request, Group $group)
    {
        $this->service->update($request, $group);
        return self::jsonResponse('success', $group->refresh());
    }

    /***
     * @param Group $group
     * @return false|string
     */
    public function destroy(Group $group)
    {
        $group->loadCount($group->relations());

        if($group->hasRelations())
        throw ValidationException::withMessages(['id' => trans('validation.has_relations')]);

        $group->delete();
        return self::jsonResponse('success');
    }

    public function clients(IndexClientRequest $request)
    {
        $clients = $this->service->clientsQuery($request);
        return ClientResource::collection($clients->paginate($request->limit, 'page', $request->page));
    }

    public function groupClients(GroupClientsRequest $request)
    {
        $clients = $this->service->clientsQuery($request);
        
        return ClientResource::collection($clients->paginate($request->limit, 'page', $request->page));
    }

    public function detachClient(DetachClientRequest $request, Group $group)
    {
        $group->clients()->detach($request->client_id);
        return self::jsonResponse('success');
    }
}