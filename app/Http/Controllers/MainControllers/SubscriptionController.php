<?php

namespace App\Http\Controllers\MainControllers;

use App\Enums\SubscriptionStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\SubscriptionRequests\DisableSubscriptionRequest;
use App\Http\Requests\SubscriptionRequests\IndexSubscriptionRequest;
use App\Http\Requests\SubscriptionRequests\StoreSubscriptionRequest;
use App\Http\Resources\Subscription\SubscriptionResource;
use App\Models\package;
use App\Models\Subscription;
use App\Services\SubscriptionService;


class SubscriptionController extends Controller
{
    protected $service;
    function __construct()
    {
        $this->middleware('permission:subscriptions', ['only' => ['index']]);
        $this->middleware('permission:subscriptions/create', ['only' => ['store']]);
        $this->middleware('permission:subscriptions/disable', ['only' => ['disable']]);
        $this->service = new SubscriptionService();
    }

    /**
     * Display a listing of the resource.
     */
    public function index(IndexSubscriptionRequest $request)
    {
        $query = Subscription::search($request->search);
        $query = $this->service->scoutSearch($query, $request);

        return SubscriptionResource::collection($query->paginate($request->limit, 'page', $request->offset));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSubscriptionRequest $request)
    {
        $request['is_paid']   = false;
        $package = $this->service->store($request);
        return self::jsonResponse('success', SubscriptionResource::make($package));
    }
    /**
     * @return false|string
     */
    public function getPackages()
    {
        $groups = package::select('id', 'name')->get()->toArray();
        return self::jsonResponse('success', $groups);
    }

    /**
     * Update the specified resource in storage.
     */
    public function disable(DisableSubscriptionRequest $request, Subscription $subscription)
    {
        $this->service->disable($subscription);
        return self::jsonResponse('success', SubscriptionResource::make($subscription));
    }


}
