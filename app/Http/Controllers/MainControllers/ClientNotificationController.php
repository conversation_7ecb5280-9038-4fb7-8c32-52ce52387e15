<?php

namespace App\Http\Controllers\MainControllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClientNotificationRequests\IndexClientNotificationRequest;
use App\Http\Resources\ClientNotification\ClientNotificationResource;
use App\Models\ClientNotification;
use App\Services\ClientNotificationService;


class ClientNotificationController extends Controller
{

    protected $service;

    function __construct()
    {
        $this->middleware('permission:client-notifications', ['only' => ['index']]);
        $this->service = new ClientNotificationService();
    }

    /**
     * @param IndexClientNotificationRequest $request
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index(IndexClientNotificationRequest $request)
    {
        $query = ClientNotification::search($request->search);
        $query = $this->service->scoutSearch($query, $request);
        
        return ClientNotificationResource::collection($query->paginate($request->limit, 'page', $request->page));
    }
}
