<?php

namespace App\Http\Controllers\MainControllers;

use App\Enums\PackageStatusEnum;
use App\Enums\PackageUnitEnum;
use App\Enums\TicketStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\PackageRequests\DisablePackageRequest;
use App\Http\Requests\PackageRequests\IndexPackageRequest;
use App\Http\Requests\PackageRequests\StorePackageRequest;
use App\Http\Requests\PackageRequests\UpdatePackageRequest;
use App\Http\Resources\Package\PackageResource;
use App\Models\Package;
use Illuminate\Validation\ValidationException;

class PackageController extends Controller
{

    function __construct()
    {
        $this->middleware('permission:packages', ['only' => ['index']]);
        $this->middleware('permission:packages/create', ['only' => ['store']]);
        $this->middleware('permission:packages/update', ['only' => ['update']]);
        $this->middleware('permission:packages/details', ['only' => ['show']]);
        $this->middleware('permission:packages/delete', ['only' => ['destroy']]);
        $this->middleware('permission:packages/disable', ['only' => ['disable']]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(IndexPackageRequest $request)
    {
        $query = Package::search($request->search)->query(function($query) use ($request){
            $unit = PackageUnitEnum::getCaseFromEnumData($request->search);
            $status = PackageStatusEnum::getCaseFromEnumData($request->search);
            if($request->search){
                if($unit)
                $query->orWhere('unit', 'like', '%' . $unit . '%');
            if($status)
                $query->orWhere('status', 'like', '%' . $status . '%');
            }
            $query = $this->orderBy($query, $request, 'packages');
        });
        return PackageResource::collection($query->paginate($request->limit, 'page', $request->offset));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StorePackageRequest $request)
    {
        $package = Package::create($request->validated());
        return self::jsonResponse('success', PackageResource::make($package));
    }
    /**
     * Display the specified resource.
     */
    public function show(Package $package)
    {
        return self::jsonResponse('success', PackageResource::make($package));
    }



    /**
     * Update the specified resource in storage.
     */
    public function update(UpdatePackageRequest $request, Package $package)
    {
        if ($package->hasRelations())
            throw ValidationException::withMessages(['id' => trans('validation.has_relations')]);
        $package->update($request->all());
        return self::jsonResponse('success', PackageResource::make($package));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Package $package)
    {
        if ($package->hasRelations())
            throw ValidationException::withMessages(['id' => trans('validation.has_relations')]);
        $package->delete();
        return self::jsonResponse('success');
    }

    /**
     * Update the specified resource in storage.
     */
    public function disable(DisablePackageRequest $request, Package $package)
    {
        $request['status']   = PackageStatusEnum::NOT_Available->value;
        $package->update($request->all());
        return self::jsonResponse('success', PackageResource::make($package));
    }
}
