<?php

namespace App\Http\Controllers\MainControllers;

use App\Enums\ServerResources;
use App\Http\Controllers\Controller;
use App\Http\Requests\ServerRequests\ServerIndexRequest;
use App\Http\Requests\ServerRequests\StoreServerRequest;
use App\Http\Requests\ServerRequests\UpdateServerRequest;
use App\Http\Resources\Provider\ProviderResource;
use App\Http\Resources\ServerResource;
use App\Http\Resources\ServerResourceResource;
use App\Models\Provider;
use App\Models\Server;
use App\Services\ServerService;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class ServerController extends Controller
{
    protected $service;

    function __construct()
    {
        $this->middleware('permission:servers', ['only' => ['index']]);
        $this->middleware('permission:servers/create', ['only' => ['store']]);
        $this->middleware('permission:servers/update', ['only' => ['update']]);
        $this->middleware('permission:servers/details', ['only' => ['show']]);
        $this->middleware('permission:servers/delete', ['only' => ['destroy']]);
        $this->service = new ServerService();
    }

    /**
     * Display a listing of the resource.
     */
    public function index(ServerIndexRequest $request)
    {
        /**
         * the second param stands for columns, so we can cast what's needed
         * in the model and call it here
         */
        $query = Server::search($request->search)->query(function ($query) use ($request) {
            $query = $this->orderBy($query, $request, 'servers');
        });
        return ServerResource::collection($query->paginate($request->limit, 'page', $request->offset));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreServerRequest $request)
    {
        $server = $this->service->store($request);
        return self::jsonResponse('success', ServerResource::make($server));
    }

    /**
     * Display the specified resource.
     */
    public function show(Server $server)
    {
        $server->load('resources');
        $server['resources_collection'] =  collect($server->resources)->keyBy('resource');
        return self::jsonResponse('success', collect(ServerResource::make($server))->merge(ServerResourceResource::make($server)));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Server $server)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateServerRequest $request, Server $server)
    {
        $this->service->update($request, $server);
        return self::jsonResponse('success', ServerResource::make($server));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Server $server)
    {
        $this->service->delete($server);
        return self::jsonResponse('success');
    }

    public function providers()
    {
        $providers = Provider::all();
        return ProviderResource::collection($providers);
    }

    public function regenerateToken(Server $server)
    {
        $token = $server->tokenize();
        return self::jsonResponse('success', $token);
    }
}
