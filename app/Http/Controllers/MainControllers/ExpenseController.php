<?php

namespace App\Http\Controllers\MainControllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\ExpenseRequests\IndexExpenseRequest;
use App\Http\Requests\ExpenseRequests\StoreExpenseRequest;
use App\Http\Requests\ExpenseRequests\UpdateExpenseRequest;
use App\Http\Resources\ExpenseResource;
use App\Http\Resources\DeviceResource;
use App\Http\Resources\ServerResource;
use App\Models\Expense;
use App\Models\Device;
use App\Models\Server;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Str;

class ExpenseController extends Controller
{

    function __construct()
    {
        $this->middleware('permission:expenses', ['only' => ['index']]);
        $this->middleware('permission:expenses/create', ['only' => ['store']]);
        $this->middleware('permission:expenses/update', ['only' => ['update']]);
        $this->middleware('permission:expenses/details', ['only' => ['show']]);
        $this->middleware('permission:expenses/delete', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(IndexExpenseRequest $request)
    {
        $query = Expense::search($request->search)
        ->query(function($query) use ($request){
            $query->with('server');
            if ($request->search)
            $query = $query->orWhereHas('server', function ($subquery) use ($request) {
                $subquery->where('name', 'like', '%' . $request->search . '%');
            });
            $query = $this->orderBy($query, $request, 'expenses');
        });
        return ExpenseResource::collection($query->paginate($request->limit, 'page', $request->page));
    }

    /**
     * @param StoreExpenseRequest $request
     * @return false|string
     */
    public function store(StoreExpenseRequest $request)
    {
        $expense = Expense::create($request->validated());
        return self::jsonResponse('success', $expense);
    }

    /***
     * @param Expense $expense
     * @return false|string
     */
    public function show(Expense $expense)
    {
        $expense->load('server');
        return self::jsonResponse('success', $expense);
    }

    /**
     * @param UpdateExpenseRequest $request
     * @param Expense $expense
     * @return false|string
     */
    public function update(UpdateExpenseRequest $request, Expense $expense)
    {
        $expense->update($request->validated());
        return self::jsonResponse('success', $expense->refresh());
    }

    /***
     * @param Expense $expense
     * @return false|string
     */
    public function destroy(Expense $expense)
    {
        $expense->loadCount($expense->relations());

        if($expense->hasRelations())
        throw ValidationException::withMessages(['id' => trans('validation.has_relations')]);

        $expense->delete();
        return self::jsonResponse('success');
    }

    public function servers()
    {
        return ServerResource::collection(Server::all());
    }

}

