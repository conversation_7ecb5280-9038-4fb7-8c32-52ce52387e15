<?php

namespace App\Http\Controllers\MainControllers;


use App\Enums\SettingEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\SettingRequests\IndexSettingRequest;
use App\Http\Requests\SettingRequests\UpdateSettingRequest;
use App\Http\Resources\Setting\SettingResource;
use App\Models\Setting;

class SettingController extends Controller
{
    function __construct()
    {
        $this->middleware('permission:settings', ['only' => ['index']]);
        $this->middleware('permission:settings/update', ['only' => ['update']]);
        $this->middleware('permission:settings/update-content', ['only' => ['update']]);
        $this->middleware('permission:settings/details', ['only' => ['show']]);
        $this->middleware('permission:settings/indexContent', ['only' => ['indexContent']]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(IndexSettingRequest $request)
    {
        $query = Setting::search($request->search)
        ->query(function($query) use ($request){
            $query = $this->orderBy($query, $request, 'settings');
        })
        ->where('is_content', false);
        return SettingResource::collection($query->paginate($request->limit, 'page', $request->page));
    }

    /**
     * @param IndexSettingRequest $request
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function indexContent(IndexSettingRequest $request)
    {
        $query     = Setting::search($request->search)->where('is_content',true)
        ->query(function($query) use ($request){
            $query = $this->orderBy($query, $request, 'settings');
        });
        return SettingResource::collection($query->paginate($request->limit, 'page', $request->page));
    }

    /***
     * @param Setting $setting
     * @return false|string
     */
    public function show(Setting $setting)
    {
        return self::jsonResponse('success', $setting);
    }

    /**
     * @param UpdateSettingRequest $request
     * @param Setting $setting
     * @return false|string
     */
    public function update(UpdateSettingRequest $request, Setting $setting)
    {
        $setting->update($request->all());
        return self::jsonResponse('success', $setting->refresh());
    }
}
