<?php

namespace App\Http\Requests\GroupRequests;

use App\Http\Requests\BaseRequest;
use Illuminate\Foundation\Http\FormRequest;

class StoreGroupRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:50', 'unique_ignore_deleted:groups,name'],
        ];
        if($this->selectAllFilterValue === '')
        $rules['clients'] = ['required','array', 'min:1', 'exists_ignore_deleted:clients,id'];
        return $rules;
    }

    public function prepareForValidation()
    {
        $clientsIds = $this->selectAllFilterValue !== '' ? [] : collect($this->clients)->unique()->toArray();
        $this->merge(['clients' => $clientsIds]);

    }
}
