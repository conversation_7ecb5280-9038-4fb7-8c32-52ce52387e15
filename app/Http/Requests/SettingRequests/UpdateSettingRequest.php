<?php

namespace App\Http\Requests\SettingRequests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
public function rules(): array
{
    return [
        'value' => [
            'sometimes',
            function ($attribute, $value, $fail) {
                $type = request()->input('type');

                switch ($type) {
                    case 'email':
                        if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                            $fail(__('The :attribute must be a valid email address.'));
                        }
                        break;

                    case 'numeric':
                        if (!is_numeric($value)) {
                            $fail(__('The :attribute must be a numeric value.'));
                        }
                        break;

                    case 'bool':
                        if (!in_array($value, [true, false, 'true', 'false', 1, 0, '1', '0'], true)) {
                            $fail(__('The :attribute must be true or false.'));
                        }
                        break;

                    case 'link':
                        if (!filter_var($value, FILTER_VALIDATE_URL)) {
                            $fail(__('The :attribute must be a valid URL.'));
                        }
                        break;
                }
            }
        ],
    ];
}
}

