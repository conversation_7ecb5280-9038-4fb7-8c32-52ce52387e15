<?php

namespace App\Http\Requests\ClientNotificationRequests;

use App\Http\Requests\BaseRequest;
use App\Http\Requests\IndexRequest;
use Illuminate\Foundation\Http\FormRequest;

class IndexClientNotificationRequest extends IndexRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = parent::rules();
        return $rules;
    }
}
