<?php

namespace App\Http\Requests\MobileRequests\TicketRequests;


use App\Http\Requests\BaseRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreTicketRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'email'],
            'subject' => ['required', 'string', 'max:50'],
            'content' => ['required', 'string'],
        ];
    }

    
    public function prepareForValidation()
    {
        if(auth()->id())
        $this->merge([
            'client_id' => auth()->id(),
        ]);
    }
}
