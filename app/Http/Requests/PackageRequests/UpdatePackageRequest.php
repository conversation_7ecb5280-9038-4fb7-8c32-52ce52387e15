<?php

namespace App\Http\Requests\PackageRequests;

use App\Enums\PackageStatusEnum;
use App\Enums\PackageUnitEnum;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class UpdatePackageRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'     => ['sometimes','string','max:50'],
            'duration' => ['sometimes','integer','min:1'],
            'price'    => ['sometimes', 'numeric'],
            'status'   => ['sometimes', Rule::in(PackageStatusEnum::asArray())],
            'unit'     => ['sometimes', Rule::in(PackageUnitEnum::asArray())]
        ];
    }
}
