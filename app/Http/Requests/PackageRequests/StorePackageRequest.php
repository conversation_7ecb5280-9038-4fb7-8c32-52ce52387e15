<?php

namespace App\Http\Requests\PackageRequests;

use App\Enums\PackageStatusEnum;
use App\Enums\PackageUnitEnum;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class StorePackageRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name'     => ['required','string','max:50'],
            'duration' => ['required','integer','min:1'],
            'price'    => ['required', 'numeric'],
            'status'   => ['required', Rule::in(PackageStatusEnum::asArray())],
            'unit'     => ['required', Rule::in(PackageUnitEnum::asArray())]
        ];
    }
}
