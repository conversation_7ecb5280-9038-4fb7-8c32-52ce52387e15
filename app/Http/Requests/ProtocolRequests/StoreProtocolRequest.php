<?php

namespace App\Http\Requests\ProtocolRequests;

use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class StoreProtocolRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'min:3', 'max:50', 'unique_ignore_deleted:protocols'],
            'code' => ['required', 'string', 'min:2', 'max:50', 'unique_ignore_deleted:protocols'],
            'template' => ['required', 'string']
        ];
    }
}
