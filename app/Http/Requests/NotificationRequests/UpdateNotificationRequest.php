<?php

namespace App\Http\Requests\NotificationRequests;

use App\Enums\NotificationsStatusEnum;
use App\Enums\NotificationsTypeEnum;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class UpdateNotificationRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */

        public function rules(): array
    {
        return [
            'subject'  => ['sometimes', 'string', 'max:50'],
            'content'  => ['sometimes', 'string'],
            'type'     => ['sometimes', Rule::in(NotificationsTypeEnum::asArray())],
            'group_id' => [function ($attribute, $value, $fail) {
                    if ($this->input('type') == NotificationsTypeEnum::Group->value) {
                        if (!$value) {
                            $fail('The group is required when type is "group".');
                        }
                    }
                },
            ],
            'scheduled_at' => ['sometimes', 'date',
                function ($attribute, $value, $fail) {
                    if (now()->greaterThan($value)) {
                        $fail('Update is only allowed before the scheduled time.');
                    }
                },
            ],
        ];
    }

}
