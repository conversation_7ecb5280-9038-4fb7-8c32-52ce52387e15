<?php

namespace App\Http\Requests\NotificationRequests;

use App\Enums\NotificationsTypeEnum;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rule;

class StoreNotificationRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
        {
            return [
                'group_id' => ['required_if:type,'.NotificationsTypeEnum::Group->value, 'exists_ignore_deleted:groups,id', 'nullable'],
                'client_id' => ['required_if:type,'.NotificationsTypeEnum::One->value, 'exists_ignore_deleted:clients,id', 'nullable'],
                'subject' => ['required', 'string', 'max:50'],
                'content' => ['required', 'string'],
                'type' => ['required', Rule::in(NotificationsTypeEnum::asArray())],
                'scheduled_at' => ['required', 'date'],
                ];
    }
}
