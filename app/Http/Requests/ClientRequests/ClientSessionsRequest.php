<?php

namespace App\Http\Requests\ClientRequests;

use App\Http\Requests\BaseRequest;

class ClientSessionsRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => ['required', 'exists_ignore_deleted:clients,id'],
            'search' => ['nullable', 'string'],
            'page' => ['nullable', 'integer'],
            'limit' => ['nullable', 'integer'],
            'sort' => ['nullable', 'string'],        
        ];
    }
}
