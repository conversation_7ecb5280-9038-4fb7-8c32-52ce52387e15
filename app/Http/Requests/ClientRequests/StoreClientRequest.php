<?php

namespace App\Http\Requests\ClientRequests;

use App\Enums\ClientStatusEnum;
use App\Enums\ClientTypesEnum;
use App\Http\Requests\BaseRequest;
use Illuminate\Validation\Rules\Password;

class StoreClientRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'string', 'email', 'max:50', 'unique_ignore_deleted:clients,email'],
            'password' => ['required', Password::min(8)->max(20)->letters()->numbers()->mixedCase()->symbols()],
            'type' => ['required', 'string', 'in:' . implode(',', ClientTypesEnum::getValues())],
            'status' => ['required', 'string', 'in:' . implode(',', ClientStatusEnum::getValues())],
            'is_verified' => ['required', 'boolean'],
            'registered_at' => ['nullable'],
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'registered_at' => now(),
            'type' => ClientTypesEnum::FREE->value,
            'status' => ClientStatusEnum::ACTIVE->value,
            'is_verified' => 1
        ]);
    }
}
