<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SessionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'token' => $this->token,
            'secret_key' => $this->secret_key,
            'last_used_at_formatted' => $this->last_used_at_formatted,
            'device_os' => $this->device_os,
            'country' => $this->whenLoaded('country'),
        ];
    }
}
