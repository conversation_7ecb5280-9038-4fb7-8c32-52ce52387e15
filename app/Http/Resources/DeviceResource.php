<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DeviceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'device_id' => $this->device_id,
            'device_os' => $this->device_os,
            'device_os_version' => $this->device_os_version,
            'app_version' => $this->app_version,
            'fcm_token' => $this->fcm_token,
            'last_connection_at' => $this->last_connection_at,
            'last_connection_at_formatted' => $this->last_connection_at_formatted
        ];
    }
}
