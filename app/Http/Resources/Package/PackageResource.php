<?php

namespace App\Http\Resources\Package;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PackageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                => $this->id,
            'name'              => $this->name,
            'duration'          => $this->duration,
            'status'            => $this->status,
            'unit'              => $this->unit,
            'price'             => $this->price,
            'visible_button'    => $this->hasRelations()?false:true,
        ];
    }
}
