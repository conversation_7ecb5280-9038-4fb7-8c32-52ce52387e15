<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ServerResource extends JsonResource
{

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'ip' => $this->ip,
            'socket_port' => $this->socket_port,
            'internal_status' => $this->internal_status,
            'external_status' => $this->external_status,
            'health_status' => $this->health_status,
            'translated_internal_status' => $this->translated_internal_status,
            'translated_external_status' => $this->translated_external_status,
            'translated_health_status' => $this->translated_health_status,
            'cost' => $this->cost,
            'is_free' => $this->is_free ? true : false,
            'provider_id' => $this->provider_id,
            'country_id' => $this->location->parent_id,
            'location_id' => $this->location_id,
            'country_name' => $this->whenLoaded('location')->parent?->name,
            'location_name' => $this->whenLoaded('location')->name,
            'token' => $this->token
        ];
    }
}
