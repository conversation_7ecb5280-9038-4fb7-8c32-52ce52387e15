<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClientResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'username' => $this->username,
            'email' => $this->email,
            'status' => $this->status,
            'type' => $this->type,
            'last_used_at' => $this->whenLoaded('lastAccessToken', function(){
                return $this->lastAccessToken?->last_used_at_formatted;
            }),
            'dropdown_name' => $this->dropdown_name,
            'country' => $this->whenLoaded('lastAccessToken', function(){
                return $this->lastAccessToken?->country?->name;
            }),
            'registered_at' => $this->registered_date
        ];
    }
}
