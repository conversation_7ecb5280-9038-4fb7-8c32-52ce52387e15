<?php

namespace App\Http\Resources\Subscription;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'                => $this->id,
            'user_name'         => $this->client->username ?? null,
            'email'             => $this->client->email ?? null,
            'from'              => $this->from,
            'to'                => $this->to,
            'is_paid'           => $this->is_paid,
            'status'            => $this->status,
            'package'           => $this->package->name ??null,
        ];
    }
}
