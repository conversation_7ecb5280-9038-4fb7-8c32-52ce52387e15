<?php
namespace App\Services\StatisticsServices;

use App\Enums\ClientStatusEnum;
use App\Enums\ClientTypesEnum;
use App\Models\Client;
use App\Models\Sanctum\PersonalAccessToken;
use App\Services\BaseService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ClientService extends BaseService
{
    
    /**
     * Get total number of users
     *
     * @return int
     */
    public function getTotalUsers()
    {
        return Client::whereStatus(ClientStatusEnum::ACTIVE->value)->count();
    }

    /**
     * Get number of free users
     *
     * @return int
     */
    public function getFreeUsers()
    {
        return Client::whereStatus(ClientStatusEnum::ACTIVE->value)
            ->whereType(ClientTypesEnum::FREE->value)
            ->count();
    }

    /**
     * Get number of paid users (premium)
     *
     * @return int
     */
    public function getPaidUsers()
    {
        return Client::whereStatus(ClientStatusEnum::ACTIVE->value)
            ->whereType(ClientTypesEnum::PREMIUM->value)
            ->count();
    }

    /**
     * Get logged in users statistics
     *
     * @return array
     */
    public function getLoggedInUsers()
    {
        $now = Carbon::now();

        // Today's logins
        $today = PersonalAccessToken::where('tokenable_type', Client::class)
            ->whereDate('last_used_at', $now->toDateString())
            ->distinct('tokenable_id')
            ->count('tokenable_id');

        // Last 7 days logins
        $last7Days = PersonalAccessToken::where('tokenable_type', Client::class)
            ->whereDate('last_used_at', '>=', $now->copy()->subDays(7)->toDateString())
            ->distinct('tokenable_id')
            ->count('tokenable_id');

        // Last 30 days logins
        $last30Days = PersonalAccessToken::where('tokenable_type', Client::class)
            ->whereDate('last_used_at', '>=', $now->copy()->subDays(30)->toDateString())
            ->distinct('tokenable_id')
            ->count('tokenable_id');

        return [
            'today' => $today,
            'last_7_days' => $last7Days,
            'last_30_days' => $last30Days,
        ];
    }

    /**
     * Get new users (registered this month)
     *
     * @return int
     */
    public function getNewUsers()
    {
        $now = Carbon::now();
        return Client::whereStatus(ClientStatusEnum::ACTIVE->value)
            ->whereMonth('registered_at', $now->month)
            ->whereYear('registered_at', $now->year)
            ->count();
    }

    /**
     * Get user distribution by country
     *
     * @return array
     */
    public function getUserDistributionByCountry()
    {
        $countryDistribution = PersonalAccessToken::
            select('locations.name as country', DB::raw('COUNT(DISTINCT personal_access_tokens.tokenable_id) as user_count'))
            ->join('locations', 'personal_access_tokens.country_id', '=', 'locations.id')
            ->where('personal_access_tokens.tokenable_type', Client::class)
            ->whereNotNull('personal_access_tokens.country_id')
            ->groupBy('locations.name')
            ->orderBy('user_count', 'desc')
            ->get();

        return $countryDistribution;
    }

    /**
     * Get free to paid conversion rate
     *
     * @return float
     */
    public function getConversionRate()
    {
        $freeUsers = $this->getFreeUsers();
        $paidUsers = $this->getPaidUsers();

        if ($freeUsers + $paidUsers > 0) {
            $conversionRate = ($paidUsers / ($freeUsers + $paidUsers)) * 100;
            return round($conversionRate, 2);
        }

        return 0;
    }   
}