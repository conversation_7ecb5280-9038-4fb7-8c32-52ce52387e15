<?php

namespace App\Services;

use App\Enums\ClientStatusEnum;
use App\Enums\ClientTypesEnum;
use App\Models\Client;
use App\Models\Group;
use DB;

class GroupService
{
    public function clientsQuery($request)
    {
        $query = Client::search($request->search)
            ->query(function ($subquery) use ($request) {
                $subquery->with('lastAccessToken.country')
                ->whereRelation('groups', 'groups.id', $request->group_id);

                if ($request->search) {
                    $subquery->where(function ($q) use ($request) {
                        $type = ClientTypesEnum::getCaseFromEnumData($request->search);
                        $status = ClientStatusEnum::getCaseFromEnumData($request->search);
                        
                        $q->orWhere('type', 'like', '%' . $type . '%');
                        $q->orWhere('status', 'like', '%' . $status . '%');
                        
                        $q->orWhereRelation('lastAccessToken.country', 'name', 'like', '%' . $request->search . '%');
                    });
                }
            })
            ;
        return $query;
    }

    public function groupClientsIds($request)
    {
        $clients = [];
        if($request->selectAllFilterValue !== '')
        $clients = $this->clientsQuery($request)->get()->pluck('id')->toArray();
        else{
            $clients = ($request->clients_ids ?? $request->clients);
        }
         return $clients;

    }

    public function store($request)
    {
        DB::transaction(function () use ($request) {
            $group = Group::create($request->validated());
            $clients = $this->groupClientsIds($request);
            $group->clients()->syncWithPivotValues($clients, ['created_by' => auth()->id(), 'updated_by' => auth()->id()]); 
        });
    }

    public function update($request, $group)
    {
        DB::transaction(function () use ($request, $group) {
            $group->update($request->validated());
        $clients = $this->groupClientsIds($request);
        $group->clients()->syncWithPivotValues($clients, ['created_by' => auth()->id(), 'updated_by' => auth()->id()]);
        });
    }
}
