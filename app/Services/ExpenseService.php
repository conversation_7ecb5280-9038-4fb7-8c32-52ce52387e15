<?php

namespace App\Services;

use App\Enums\ExpenseTypesEnum;
use App\Enums\ServerResources;
use App\Http\Requests\ServerRequests\StoreServerRequest;
use App\Http\Requests\ServerRequests\UpdateServerRequest;
use App\Models\Expense;
use App\Models\Server;
use App\Models\Setting;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ExpenseService
{
    public function store(StoreServerRequest $request)
    {
        DB::beginTransaction();
        try {
            $server = Server::create([
                'location_id' => $request->validated('location_id'),
                'provider_id' => $request->validated('provider_id'),
                'name' => $request->validated('name'),
                'ip' => $request->validated('ip'),
                'socket_port' => $request->validated('socket_port'),
                'cost' => $request->validated('cost'),
                'is_free' => $request->validated('is_free'),
            ]);
            collect(ServerResources::asArray())->map(function ($resource) use ($server, $request) {
                $resourceDefaultThreshold = Setting::where('code', ServerResources::from($resource)->code())->firstOrFail();
                $server->resources()->create([
                    'resource' => $resource,
                    'value' => $request->validated($resource),
                    // ToDo when settings table will be finished
                    'threshold' => $request->validated($resource . '_threshold') ?? $resourceDefaultThreshold['value']
                ]);
            });
            Expense::create([
                'amount' => $request->validated('cost'),
                'description' => "server cost",
                'type' => ExpenseTypesEnum::SERVER->value,
                'server_id' => $server['id'],
            ]);
            DB::commit();
            return $server;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function update(UpdateServerRequest $request, Server $server)
    {
        DB::beginTransaction();
        try {
            $server->update([
                'location_id' => $request->validated('location_id'),
                'provider_id' => $request->validated('provider_id'),
                'name' => $request->validated('name'),
                'ip' => $request->validated('ip'),
                'socket_port' => $request->validated('socket_port'),
                'cost' => $request->validated('cost'),
                'is_free' => $request->validated('is_free'),
            ]);
            collect(ServerResources::asArray())->map(function ($resource) use ($server, $request) {
                $server->resources()->where('resource', $resource)->update([
                    'value' => $request->validated($resource),
                    // ToDo when settings table will be finished
                    'threshold' => 1
                ]);
            });
            //ToDo Entering a new expense when expeses table will be finished
            DB::commit();
            return $server;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }
}
