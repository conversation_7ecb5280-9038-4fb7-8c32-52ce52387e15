<?php

namespace App\Services;

use App\Enums\ClientTypesEnum;
use App\Enums\PackageUnitEnum;
use App\Enums\SubscriptionStatusEnum;
use App\Http\Requests\SubscriptionRequests\StoreSubscriptionRequest;
use App\Models\Client;
use App\Models\package;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;


class SubscriptionService extends BaseService
{
    /**
     * Store a new subscription
     *
     * @param StoreSubscriptionRequest $request
     * @return Subscription
     * @throws \Exception
     */
    public function store(StoreSubscriptionRequest $request)
    {
        DB::beginTransaction();
        try {
            $package_id = $request->validated('package_id');
            $client_id  = $request->validated('client_id');
            $client     = Client::find($client_id);
            $subscription_date = $this->getDateByPackage($package_id);
            $subscription = Subscription::create([
                'package_id' => $package_id,
                'client_id'  => $client_id,
                'status'     => SubscriptionStatusEnum::ACTIVE->value,
                'from'       => $subscription_date['from'],
                'to'         => $subscription_date['to'],
                'is_paid'    => $request['is_paid'],
            ]);
            if(!$subscription)
                throw new \Exception('Error when create subscription');
            if(!$client->update(['type' => ClientTypesEnum::PREMIUM->value]))
                throw new \Exception('Error when update client type');

            DB::commit();
            return $subscription;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    /**
     * Disable a subscription and update client type
     *
     * @param Subscription $subscription
     * @return Subscription
     * @throws \Exception
     */
    public function disable(Subscription $subscription)
    {
        DB::beginTransaction();
        try {
            $client = $subscription->client;

            if ($subscription->disable()) {
                if (!$client->update(['type' => ClientTypesEnum::FREE->value])) {
                    throw new \Exception('Error when update client type');
                }
            }

            DB::commit();
            return $subscription;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    /**
     * Calculate subscription dates based on package
     *
     * @param int $package_id
     * @return array
     * @throws \Exception
     */
    public function getDateByPackage($package_id)
    {
        $package     = package::find($package_id);
        $unit        = $package->unit;
        $duration    = $package->duration;
        $from        = Carbon::now();
        switch ($unit) {
            case PackageUnitEnum::Days->value:
                $to = $from->copy()->addDays($duration);
                break;
            case PackageUnitEnum::Months->value:
                $to = $from->copy()->addMonths($duration);
                break;
            case PackageUnitEnum::Years->value:
                $to = $from->copy()->addYears($duration);
                break;
            default:
                throw new \Exception("Invalid unit type for this package".$package->name);
        }
        return [
            'from' => $from->format('Y-m-d'),
            'to'   => $to->format('Y-m-d'),
        ];
    }

    public function scoutSearch($query, $request)
    {
        $query = $query
        ->query(function ($subscription) use ($request) {
            $subscription->with('client', 'package');
            $subscription = $this->orderBy($subscription, $request, 'subscriptions');
            if($request->search){
                $status = SubscriptionStatusEnum::getCaseFromEnumData($request->search);
                $subscription = $subscription->orWhereRelation('client', 'username', 'like', '%' . $request->search . '%');
                $subscription = $subscription->orWhereRelation('client', 'email', 'like', '%' . $request->search . '%');
                if($status)
                $subscription->orWhere('status', 'like', '%' . $status . '%');
            }

            if ($request->package_id)
            $subscription->where('package_id', $request->package_id);
            if ($request->client_id)
            $subscription->where('client_id', $request->client_id);
        });
        return $query;
    }
}
