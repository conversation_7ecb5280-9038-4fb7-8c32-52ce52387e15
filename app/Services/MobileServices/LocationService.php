<?php

namespace App\Services\MobileServices;

use App\Enums\ClientTypesEnum;
use App\Models\Location;
use App\Models\Server;

class LocationService
{
    public function getPremiumCountries()
    {
        $isUserPremium = auth()->user()->type === ClientTypesEnum::PREMIUM->value;
        return Location::query()
            ->whereHas('servers', function ($server) {
                $server->enabled()->up()->premium();
            })
            ->withCount(['servers' => function ($server) {
                $server->enabled()->up()->premium();
            }])
            ->whereNull('parent_id')
            ->get()
            ->map(function ($country) use ($isUserPremium) {
                $country['is_locked'] = $isUserPremium ? 0 : 1;
                return $country;
            })
            ;
    }

    public function getFreeCountries()
    {
        return Location::query()
            ->whereHas('servers', function ($server) {
                $server->enabled()->up()->free();
            })
            ->withCount(['servers' => function ($server) {
                $server->enabled()->up()->free();
            }])
            ->whereNull('parent_id')
            ->get()
            ->map(function ($country) {
                $country['is_locked'] = 0;
                return $country;
            })
            ;
    }

    public function getAllCountries()
    {
        return Location::query()
            ->whereHas('servers', function ($server) {
                $server->enabled()->up();
            })
            ->withCount(['servers' => function ($server) {
                $server->enabled()->up();
            }])
            ->whereNull('parent_id')
            ->get();
    }

    public function getCountries()
    {
        if( auth()->user()->isPremium() )
            return $this->getAllCountries();

        $freeCountries = $this->getFreeCountries();
        $premiumCountries = $this->getPremiumCountries();
        $countries = $freeCountries->push($premiumCountries)->flatten();
        return $countries;
    }
}
