<?php

namespace App\Helpers;

use App\Jobs\SendMailJob;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Facades\Mail;

class MailHelper
{
    public static function send(string $email, Mailable $mailable, $mailer = null)
    {
        $mail = $mailer ? Mail::mailer($mailer) : Mail::to($email);
        $mail->send($mailable);
        return true;
    }

    public static function queue(string $email, Mailable $mailable, $mailer = null)
    {
        SendMailJob::dispatch($email, $mailable, $mailer);
        return true;
    }
}

