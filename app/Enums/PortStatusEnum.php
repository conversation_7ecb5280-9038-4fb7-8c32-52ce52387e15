<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum PortStatusEnum: string
{
    /**
     * Example of enum
     */
    use EnumTrait;

    case ENABLED = 'enabled';
    case DISABLED = 'disabled';

    const ENUM_DATA = [
        self::ENABLED->value => [
            'title_en' => 'Enabled',
            'title_ar' => 'فعال',
            'value' => 'enabled',
        ],
        self::DISABLED->value => [
            'title_en' => 'Disabled',
            'title_ar' => 'غير فعال',
            'value' => 'disabled',
        ]
    ];
}

