<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum ServerHealthStatus: string
{
    /**
     * Example of enum
     */
    use EnumTrait;

    case HEALTHY = 'healthy';
    case NOT = 'not';

    public static function asArray(): array
    {
        return array_map(fn($x) => $x->value, self::cases());
    }

    const ENUM_DATA = [
        self::HEALTHY->value => [
            'title_en' => 'Healthy',
            'title_ar' => 'صحي',
            'value' => 'healthy',
        ],
        self::NOT->value => [
            'title_en' => 'Not',
            'title_ar' => 'غير صحي',
            'value' => 'not',
        ]
    ];
}

