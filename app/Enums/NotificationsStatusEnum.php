<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum NotificationsStatusEnum: string
{
    use EnumTrait;

    case Scheduled      = 'scheduled';
    case Triggered      = 'triggered';


    public static function asArray(): array
    {
        return array_map(fn($x) => $x->value, self::cases());
    }

    const ENUM_DATA = [
        self::Scheduled->value => [
            'title_en' => 'Scheduled',
            'title_ar' => 'مجدول',
            'value' => 'scheduled',
        ],
        self::Triggered->value => [
            'title_en' => 'Triggered',
            'title_ar' => 'تم التنفيذ',
            'value' => 'triggered',
        ],
    ];
}
