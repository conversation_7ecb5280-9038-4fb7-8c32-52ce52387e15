<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum ClientStatusEnum: string
{
    use EnumTrait;
    
    case ACTIVE = 'active';
    case BLOCKED = 'blocked';

    
    const ENUM_DATA = [
        self::ACTIVE->value => [
            'title_en' => 'Active',
            'title_ar' => 'نشط',
            'value' => 'active',
        ],
        self::BLOCKED->value => [
            'title_en' => 'Blocked',
            'title_ar' => 'محظور',
            'value' => 'blocked',
        ],
    ];
}

