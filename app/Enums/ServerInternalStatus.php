<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum ServerInternalStatus: string
{
    /**
     * Example of enum
     */
    use EnumTrait;

    case ENABLED = 'enabled';
    case DISABLED = 'disabled';

    public static function asArray(): array
    {
        return array_map(fn($x) => $x->value, self::cases());
    }

    const ENUM_DATA = [
        self::ENABLED->value => [
            'title_en' => 'Enabled',
            'title_ar' => 'فعال',
            'value' => 'enabled',
        ],
        self::DISABLED->value => [
            'title_en' => 'Disabled',
            'title_ar' => 'غير فعال',
            'value' => 'disabled',
        ]
    ];
}

