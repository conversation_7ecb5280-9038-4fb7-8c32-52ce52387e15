<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum ClientNotificationsStatusEnum: string
{
    use EnumTrait;

    case Sending  = 'sending';
    case Sent     = 'sent';
    case Read     = 'read';


    public static function asArray(): array
    {
        return array_map(fn($x) => $x->value, self::cases());
    }

    const ENUM_DATA = [
        self::Sending->value => [
            'title_en' => 'Sending',
            'title_ar' => 'جاري الإرسال',
            'value' => 'sending',
        ],
        self::Sent->value => [
            'title_en' => 'Sent',
            'title_ar' => 'تم الإرسال',
            'value' => 'sent',
        ],
        self::Read->value => [
            'title_en' => 'Read',
            'title_ar' => 'مقروء',
            'value' => 'read',
        ],
    ];
}
