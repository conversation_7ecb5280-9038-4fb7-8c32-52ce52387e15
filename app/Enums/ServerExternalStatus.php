<?php

namespace App\Enums;

use App\Traits\EnumTrait;

enum ServerExternalStatus: string
{
    /**
     * Example of enum
     */
    use EnumTrait;

    case UP = 'up';
    case DOWN = 'down';

    public static function asArray(): array
    {
        return array_map(fn($x) => $x->value, self::cases());
    }

    const ENUM_DATA = [
        self::UP->value => [
            'title_en' => 'Up',
            'title_ar' => 'يعمل',
            'value' => 'up',
        ],
        self::DOWN->value => [
            'title_en' => 'Down',
            'title_ar' => 'معطل',
            'value' => 'down',
        ]
    ];
}

