<?php

namespace App\Models;

use App\Http\Requests\ProtocolRequests\UpdateProtocolRequest;
use App\Traits\BaseTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON><PERSON>\Scout\Searchable;

class Protocol extends BaseModel
{
    /** @use HasFactory<\Database\Factories\ProtocolFactory> */
    use Searchable;

    protected $guarded = [];


    public function toSearchableArray(): array
    {
        return [
            'name' => '',
            'code' => '',
        ];
    }

    /**
     * @param UpdateProtocolRequest $request
     * @return bool
     */
    public function edit(UpdateProtocolRequest $request): bool
    {
        return $this->update([
            'name' => $request->validated('name'),
            'code' => $request->validated('code'),
            'template' => $request->validated('template'),
            'version' => $this->version + 1
        ]);
    }
}
