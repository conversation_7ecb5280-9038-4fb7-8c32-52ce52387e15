<?php

namespace App\Models;

use App\Traits\BaseTrait;
use <PERSON><PERSON>\Scout\Searchable;

class Group extends BaseModel
{
    use Searchable, BaseTrait;
    protected $guarded = ['clients','clients_ids'];

    public function clients()
    {
        return $this->belongsToMany(Client::class, 'group_clients');
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    
    public static function relations(){
        return [
            'clients',
            'notifications'
        ];
    }

    public function hasRelations(): bool
    {
        foreach ($this->relations() as $key => $value) {
            if($this[$value . '_count'])
            return true;
        }

        return false;
    }


}
