<?php

namespace App\Models;

use App\Enums\PackageStatusEnum;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\HasMany;
use <PERSON><PERSON>\Scout\Searchable;
use App\Models\Subscription;

class Package extends BaseModel
{
    use Searchable;

    protected $table = 'packages';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'price',
        'duration',
        'status',
        'unit',
    ];


    /**
     * @return bool
     */
    public function hasRelations(): bool
    {
        if (sizeof($this->subscriptions) > 0)
            return true;
        return false;
    }

    /**
     * @return HasMany
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * @return bool
     */
    public function toggleActivation(): bool
    {
        $status = PackageStatusEnum::NOT_Available->value;
        if($this->status == PackageStatusEnum::NOT_Available->value){
            $status = PackageStatusEnum::Available->value;
        }
        return $this->update(['status' => $status]);
    }

    /**
     * Define search keys
     *
     * @return array
     */
    public function toSearchableArray(): array
    {
        return [
            'name' => $this->name,
            'price' => $this->price,
            'duration' => $this->duration,
            'status' => $this->status,
            'unit' => $this->unit
        ];
    }
}
