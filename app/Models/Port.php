<?php

namespace App\Models;

use App\Enums\PortStatusEnum;
use App\Enums\ServerHealthStatus;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Laravel\Scout\Searchable;

class Port extends BaseModel
{
    use  Searchable;

    protected $guarded = ['id'];

    public function toSearchableArray(): array
    {
        return [
            'port' => '',
            "server_public_key" => '',
            "control_ip_port" => '',
            "purpose" => '',
            "current_connections_count" => '',
            "connections_threshold" => '',
        ];
    }

    public static function relations()
    {
        return [
            //
        ];
    }

    public function hasRelations(): bool
    {
        $this->loadCount($this->relations());
        foreach ($this->relations() as $key => $value) {
            if ($this[$value . '_count'])
                return true;
        }

        return false;
    }

    public function protocol()
    {
        return $this->belongsTo(Protocol::class);
    }

    public function server()
    {
        return $this->belongsTo(Server::class);
    }

    public function scopeEnabled($query): void
    {
        $query->where('status', PortStatusEnum::ENABLED->value);
    }

    public function scopeAvailableServers(Builder $query)
    {
        return $query->enabled()
            ->whereHas('server', function ($subQuery) {
                $subQuery->healthy()
                    ->enabled()
                    ->up();
            });
    }

    public function scopeFilterLocation(Builder $query, int $countryId)
    {
        return $query->whereRelation('server', 'location_id', $countryId)
                    ->orWhereRelation('server.location', 'parent_id', $countryId);
    }

    public function scopeFilterProtocol(Builder $query, int $protocolId)
    {
        return $query->where('protocol_id', $protocolId);
    }

    public function scopeFree(Builder $query)
    {
        return $query->whereHas('server', function ($query) {
            $query->where('is_free', 1);
        });
    }
}
