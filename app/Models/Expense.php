<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Laravel\Scout\Searchable;

class Expense extends BaseModel
{
    use Searchable;
    protected $guarded = [];

    /**
     * Define search keys
     *
     * @return array
     */
    public function toSearchableArray(): array
    {
        return [
            'amount' => '',
            'description' => '',
            'payment_date' => '',
        ];
    }

    public function hasRelations(): bool
    {
        foreach ($this->relations() as $key => $value) {
            if($this[$value . '_count'])
            return true;
        }

        return false;
    }

    public static function relations(){
        return [
            //
        ];
    }

    public function server()
    {
        return $this->belongsTo(Server::class)->withTrashed();
    }

    public function scopeDuringMonthOffset(Builder $query, int $monthsAgo)
    {
        $startOfMonth = Carbon::now()->subMonthNoOverflow($monthsAgo)->startOfMonth();
        $endOfMonth = Carbon::now()->subMonthNoOverflow($monthsAgo)->endOfMonth();

        return $query->whereBetween('payment_date', [$startOfMonth, $endOfMonth]);
    }
}
