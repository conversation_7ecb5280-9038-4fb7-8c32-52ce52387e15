<?php

namespace App\Models;

use App\Enums\ExpenseTypesEnum;
use App\Enums\ServerExternalStatus;
use App\Enums\ServerHealthStatus;
use App\Enums\ServerInternalStatus;
use App\Traits\BaseTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Scout\Searchable;

class Server extends Model
{
    /** @use HasFactory<\Database\Factories\ServerFactory> */
    use HasApiTokens, HasFactory, BaseTrait, SoftDeletes, Searchable;

    protected $guarded = [];

    /**
     * Model Relations
     */
    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    public function location()
    {
        return $this->belongsTo(Location::class);
    }

    public function resources()
    {
        return $this->hasMany(ServerResource::class);
    }

    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }

    /**
     * Model Attributes
     */
    public function getTranslatedInternalStatusAttribute()
    {
        return ServerInternalStatus::getCase($this->internal_status);
    }

    public function getTranslatedExternalStatusAttribute()
    {
        return ServerExternalStatus::getCase($this->external_status);
    }

    public function getTranslatedHealthStatusAttribute()
    {
        return ServerHealthStatus::getCase($this->health_status);
    }

    /**
     * Model Methods
     */
    public function updateOrCreateExpense()
    {
        return Expense::updateOrCreate(
            ['server_id' => $this->id],
            [
                'amount' => $this->cost,
                'description' => "server cost",
                'type' => ExpenseTypesEnum::SERVER->value,
                'payment_date' => now()
            ]
        );
    }

    public function updateHealthStatus(bool $isHealthy)
    {
        return $this->update([
            'health_status' => $isHealthy ? ServerHealthStatus::HEALTHY->value : ServerHealthStatus::NOT->value
        ]);
    }

    public function tokenize()
    {
        $token = $this->createToken($this->name)->plainTextToken;
        $this->update([
            'token' => $token
        ]);
        return $token;
    }

    /**
     * Define search keys
     *
     * @return array
     */
    public function toSearchableArray(): array
    {
        return [
            'name' => '',
            'ip' => '',
        ];
    }

    public function ports()
    {
        return $this->hasMany(Port::class);
    }

    public function protocols()
    {
        return $this->belongsToMany(Protocol::class, 'ports');
    }

    public function tcpUsers()
    {
        return $this->hasMany(Port::class)->whereRelation('protocol', 'name', 'tcp');
    }

    public function udpUsers()
    {
        return $this->hasMany(Port::class)->whereRelation('protocol', 'name', 'udp');
    }

    public function wireguardUsers()
    {
        return $this->hasMany(Port::class)->whereRelation('protocol', 'name', 'wireguard');
    }

    public static function relations()
    {
        return [
            'ports',
            'expenses'
        ];
    }

    public function hasRelations(): bool
    {
        $this->loadCount($this->relations());
        foreach ($this->relations() as $key => $value) {
            if ($this[$value . '_count'])
                return true;
        }

        return false;
    }

    public function scopeEnabled($query): void
    {
        $query->where('internal_status', ServerInternalStatus::ENABLED->value);
    }

    public function scopeUp($query): void
    {
        $query->where('external_status', ServerExternalStatus::UP->value);
    }

    public function scopeHealthy($query): void
    {
        $query->where('health_status', ServerHealthStatus::HEALTHY->value);
    }

    public function scopeFree($query): void
    {
        $query->where('is_free', 1);
    }

    public function scopePremium($query): void
    {
        $query->where('is_free', 0);
    }

    public function isUp()
    {
        return $this->external_status == ServerExternalStatus::UP->value;
    }

    public function isDown()
    {
        return $this->external_status == ServerExternalStatus::DOWN->value;
    }
}
