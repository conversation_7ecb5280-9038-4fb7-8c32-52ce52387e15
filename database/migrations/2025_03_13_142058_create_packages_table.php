<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use \App\Enums\PackageStatusEnum;
use \App\Enums\PackageUnitEnum;
use \App\Traits\LogColumns;

return new class extends Migration
{
    use LogColumns;
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('packages', function (Blueprint $table) {
            $table->id();
            $table->string('name',50);
            $table->enum('unit',PackageUnitEnum::asArray());
            $table->enum('status',PackageStatusEnum::asArray());
            $table->integer('duration');
            $table->decimal('price',5,2);
            $this->LogColumns($table);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('packages');
    }
};
