<?php

use App\Enums\PortStatusEnum;
use App\Traits\LogColumns;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    use LogColumns;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ports', function (Blueprint $table) {
            $table->id();
            $table->foreignId('server_id')->constrained();
            $table->foreignId('protocol_id')->constrained();
            $table->integer('connections_threshold')->unsigned();
            $table->integer('current_connections_count')->unsigned()->default(0);
            $table->string('ip', 50);
            $table->string('port', 4);
            $table->enum('status', PortStatusEnum::getValues())->default(PortStatusEnum::ENABLED->value);
            $table->string('control_ip_port', 150)->nullable();
            $this->LogColumns($table);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ports');
    }
};
