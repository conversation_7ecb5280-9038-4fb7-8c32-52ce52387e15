<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use \App\Enums\ClientNotificationsStatusEnum;
use App\Traits\LogColumns;

return new class extends Migration
{
    use LogColumns;
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('client_notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->references('id')->on('clients');
            $table->foreignId('notification_id')->references('id')->on('notifications');
            $table->enum('status',ClientNotificationsStatusEnum::asArray());
            $table->timestamp('sent_at');
            $this->LogColumns($table);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('client_notifications');
    }
};
