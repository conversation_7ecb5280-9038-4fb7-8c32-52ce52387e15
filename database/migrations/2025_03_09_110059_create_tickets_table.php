<?php

use App\Traits\LogColumns;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use \App\Enums\TicketStatusEnum;

return new class extends Migration
{
    use LogColumns;
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('client_id')->nullable();
            $table->string('email');
            $table->string('subject', 50);
            $table->text('content');
            $table->text('note')->nullable();
            $table->enum('status',TicketStatusEnum::asArray());
            $table->foreign('client_id')->references('id')->on('clients');
            $table->timestamp('notes_at')->nullable();
            $this->LogColumns($table, true);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
};
