<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('providers', function (Blueprint $table) {
            $table->string('contact_number', 50)->nullable()->after('name');
            $table->string('email', 100)->nullable()->after('contact_number');
            $table->string('website', 255)->nullable()->after('email');
            $table->string('admin_url', 255)->nullable()->after('website');
            $table->text('note')->nullable()->after('admin_url');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('providers', function (Blueprint $table) {
            $table->dropColumn('contact_number');
            $table->dropColumn('email');
            $table->dropColumn('website');
            $table->dropColumn('admin_url');
            $table->dropColumn('note');
        });
    }
};
