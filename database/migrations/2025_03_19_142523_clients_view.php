<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('DROP VIEW IF EXISTS clients_view');
        DB::statement('
    CREATE VIEW clients_view AS
    SELECT
        clients.username,
        clients.type,
        personal_access_tokens.secret_key,
        personal_access_tokens.token
    FROM
        clients
    INNER JOIN personal_access_tokens
        ON personal_access_tokens.tokenable_id = clients.id
        AND personal_access_tokens.tokenable_type = "App\\\\Models\\\\Client"
    WHERE personal_access_tokens.id = (
        SELECT sub_pat.id
        FROM personal_access_tokens AS sub_pat
        WHERE sub_pat.tokenable_id = clients.id
            AND sub_pat.tokenable_type = "App\\\\Models\\\\Client"
        ORDER BY sub_pat.last_used_at DESC
        LIMIT 1
    )
');

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('DROP VIEW IF EXISTS clients_view');
    }
};
