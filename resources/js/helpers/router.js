import {createRouter, createWebHistory} from 'vue-router'
import AuthRoutes from "../modules/authentication/auth-routes.js";
import DashboardRoutes from "../modules/dashboard/dashboard-routes.js";
import UsersManagementRoutes from "../modules/users-management/users-management-routes.js";
import LocationsRoutes from "../modules/settings/locations-routes.js";
import store from "@/store/store.js";
import {notify} from "@kyvg/vue3-notification";
import i18n from "@/plugins/i18n.js";
import ServersManagementRoutes from '@/modules/servers-management/servers-management-routes.js';
import ProtocolsManagementRoutes from '@/modules/protocols-management/protocols-management-routes.js';
import ProviderRoutes from "@/modules/providers-management/provider-routes";
import ClientsManagementRoutes from '@/modules/clients-management/clients-management-routes.js';
import TicketRoutes from "@/modules/tickets-management/tickets-management-routes";
import SettingsRoutes from "@/modules/settings/settings-routes";
import ExpensesRoutes from "@/modules/expenses-management/expenses-management-routes";
import GroupsManagementRoutes from '@/modules/groups-management/groups-management-routes.js';
import NotificationsManagementRoutes from "@/modules/notifications-management/notifications-management-routes";
import PackagesManagementRoutes from "@/modules/packages-management/packages-management-routes";
import ReportsRoutes from '@/modules/reports/reports-routes.js';


const routes = [
    ...GroupsManagementRoutes,
    ...ClientsManagementRoutes,
    ...LocationsRoutes,
    ...NotificationsManagementRoutes,
    ...SettingsRoutes,
    ...TicketRoutes,
    ...ProviderRoutes,
    ...UsersManagementRoutes,
    ...DashboardRoutes,
    ...ServersManagementRoutes,
    ...AuthRoutes,
    ...ProtocolsManagementRoutes,
    ...ExpensesRoutes,
    ...PackagesManagementRoutes,
    ...ReportsRoutes,
    {
        path: "/:catchAll(.*)",
        name: "NotFound",
        redirect: {name: 'dashboard'},
        meta: {
            requiresAuth: false
        }
    }
];

const router = createRouter({
    history: createWebHistory(),
    routes,
});

router.beforeEach((to, from, next) => {
    const publicPages = ['/login'];
    const authRequired = !publicPages.includes(to.path);
    const loggedIn = store.state.auth.status.loggedIn;
    // trying to access a restricted page + not logged in
    // redirect to login page
    if (authRequired && !loggedIn) {
        next('/login');
    } else if (authRequired && loggedIn) {
        if ((to.name === 'dashboard') || store.state.auth.user.permissions.includes(to.name)) {
            next();
        } else {
            notify(i18n.global.t('unauthorized'));
            next('/');
        }
    } else {
        if (!authRequired && loggedIn && to.path === '/login')
            next('/')
        next();
    }
});

export default router;
