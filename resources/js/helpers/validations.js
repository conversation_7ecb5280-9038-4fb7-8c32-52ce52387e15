import * as validators from '@vuelidate/validators';

export default function useValidations(t) {

    const ipPort = (value) => {
        const parts = value.split("]:");
        if (!value.includes("[") || !value.includes("]:")) {
            // Try regular IPv4:port format
            const ipv4Parts = value.split(":");
            if (ipv4Parts.length !== 2) {
                return false; // Incorrect format
            }

            const ipAddress = ipv4Parts[0].trim();
            const portStr = ipv4Parts[1].trim();

            // IPv4 validation
            const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
            const isValidIPv4 =
                ipv4Regex.test(ipAddress) &&
                ipAddress
                    .split(".")
                    .every(
                        (part) => parseInt(part) >= 0 && parseInt(part) <= 255
                    );

            if (!isValidIPv4) {
                return false;
            }

            const port = parseInt(portStr, 10);
            return !(
                isNaN(port) ||
                !Number.isInteger(port) ||
                port < 0 ||
                port > 65535 ||
                /[^0-9]/.test(portStr)
            );
        }

        // Handle IPv6 format [IPv6]:port
        const ipAddress = value.split("]")[0].substring(1);
        const portStr = parts[1].trim();

        // IPv6 validation
        const ipv6Regex =
            /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}::([0-9a-fA-F]{1,4}:){0,5}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}(:[0-9a-fA-F]{1,4}){1}::([0-9a-fA-F]{1,4}:){0,4}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}(:[0-9a-fA-F]{1,4}){2}::([0-9a-fA-F]{1,4}:){0,3}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}(:[0-9a-fA-F]{1,4}){3}::([0-9a-fA-F]{1,4}:){0,2}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}(:[0-9a-fA-F]{1,4}){4}::([0-9a-fA-F]{1,4}:){0,1}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}(:[0-9a-fA-F]{1,4}){5}::[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}(:[0-9a-fA-F]{1,4}){6}::$/;
        const isValidIPv6 = ipv6Regex.test(ipAddress);

        if (!isValidIPv6) {
            return false;
        }

        const port = parseInt(portStr, 10);
        return !(
            isNaN(port) ||
            !Number.isInteger(port) ||
            port < 0 ||
            port > 65535 ||
            /[^0-9]/.test(portStr)
        );
    }















    

  const validationRules = {
      email: (value) =>
          validators.email.$validator(value) || t("validation.email"),

      mobile: (value) => /^09[0-9]{8}$/.test(value) || t("validation.mobile"),

      phone: (value) => (!validators.helpers.req(value)) || /^0[0-9]{9}$/.test(value) || t("validation.phone"),

      password: (value) =>
          !validators.helpers.req(value) ||
          /^(?=.*[0-9])(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]{8,20}$/.test(
              value
          ) ||
          t("validation.password"),

      required: (value) =>
          validators.required.$validator(value) || t("validation.required"),

      optional: (value) =>
          /^(?!\s)(?!\s+$).*/.test(value) || t("validation.optional"),

      minLength: (length) => (value) =>
          validators.minLength(length).$validator(value) ||
          t("validation.minLength", { length }),

      maxLength: (length) => (value) =>
          validators.maxLength(length).$validator(value) ||
          t("validation.maxLength", { length }),

      minValue: (min) => (value) =>
          validators.minValue(min).$validator(value) ||
          t("validation.minValue", { min }),

      maxValue: (max) => (value) =>
          validators.maxValue(max).$validator(value) ||
          t("validation.maxValue", { max }),

      alpha: (value) =>
          validators.alpha.$validator(value) || t("validation.alpha"),

      alphaNum: (value) =>
          validators.alphaNum.$validator(value) || t("validation.alphaNum"),

      numeric: (value) =>
          validators.numeric.$validator(value) || t("validation.numeric"),

      integer: (value) =>
          validators.integer.$validator(value) || t("validation.integer"),

      decimal: (value) =>
          validators.decimal.$validator(value) || t("validation.decimal"),

      between: (min, max) => (value) =>
          validators.between(min, max).$validator(value) ||
          t("validation.between", { min, max }),

      url: (value) => validators.url.$validator(value) || t("validation.url"),

    ipAddress: (value) =>
        !validators.helpers.req(value) ||
        (function () {
            // Basic IPv4 validation
            const isValidIPv4 = validators.ipAddress.$validator(value);

            // IPv6 validation
            const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}::([0-9a-fA-F]{1,4}:){0,5}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}(:[0-9a-fA-F]{1,4}){1}::([0-9a-fA-F]{1,4}:){0,4}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}(:[0-9a-fA-F]{1,4}){2}::([0-9a-fA-F]{1,4}:){0,3}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}(:[0-9a-fA-F]{1,4}){3}::([0-9a-fA-F]{1,4}:){0,2}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}(:[0-9a-fA-F]{1,4}){4}::([0-9a-fA-F]{1,4}:){0,1}[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}(:[0-9a-fA-F]{1,4}){5}::[0-9a-fA-F]{1,4}$|^[0-9a-fA-F]{1,4}(:[0-9a-fA-F]{1,4}){6}::$/;
            const isValidIPv6 = ipv6Regex.test(value);

            return isValidIPv4 || isValidIPv6;
        })() ||
        t("validation.ipAddress"),
        
      ipPort: (value) =>
          !validators.helpers.req(value) ||
          ipPort(value) || t("validation.ipPort"),

      sameAs: (comparedValue) => (value) =>
          validators.sameAs(comparedValue).$validator(value) ||
          t("validation.sameAs", { comparedValue }),

      requiredIf:
          (condition, message = "") =>
          (value) =>
              validators.requiredIf(condition).$validator(value) ||
              t("validation.requiredIf", { message }),

      requiredUnless:
          (condition, message = "") =>
          (value) =>
              validators.requiredUnless(condition).$validator(value) ||
              t("validation.requiredUnless", { message }),

      contains: (param) => (value) =>
          !helpers.req(value) ||
          value.includes(param) ||
          t("validation.contains", { string }),

      startsWith: (string) => (value) =>
          !helpers.req(value) ||
          value.startsWith(string) ||
          t("validation.startsWith", { string }),

      endsWith: (string) => (value) =>
          !helpers.req(value) ||
          value.endsWith(string) ||
          t("validation.endsWith", { string }),

      beforeOrEqual: (toDate) => (value) =>
          new Date(toDate) >= new Date(value) ||
          t("validation.beforeOrEqual", { toDate }),

      before: (toDate) => (value) =>
          new Date(toDate) > new Date(value) ||
          t("validation.before", { toDate }),

      afterOrEqual: (fromDate) => (value) =>
          new Date(fromDate) <= new Date(value) ||
          t("validation.afterOrEqual", { fromDate }),

      after: (fromDate) => (value) =>
          new Date(fromDate) < new Date(value) ||
          t("validation.after", { fromDate }),
  };

  return validationRules;
}