export default function ClientNotificationStatusEnum() {

    const cases = {
        SENDING: 'sending',
        SENT: 'sent',
        READ: 'read',
    };

    const enumData = {
        sending: {
            title_en: 'Sending',
            title_ar: 'جاري الإرسال',
            value: 'sending',
        },
        sent: {
            title_en: 'Sent',
            title_ar: 'تم الإرسال',
            value: 'sent',
        },
        read: {
            title_en: 'Read',
            title_ar: 'مقروء',
            value: 'read',
        },
    };

    return {
        cases,
        enumData
    }
}
