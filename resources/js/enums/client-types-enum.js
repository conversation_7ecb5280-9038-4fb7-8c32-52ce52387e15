
export default function ClientTypesEnum() {


    const cases = {
        FREE: 'free',
        GUEST: 'guest',
        PREMIUM: 'premium',
    };

    const enumData = {
        free: {
            title_en: 'Free',
            title_ar: 'مجاني',
            value: 'free',
        },
        guest: {
            title_en: 'Guest',
            title_ar: 'زائر',
            value: 'guest',
        },
        premium: {
            title_en: 'Premium',
            title_ar: 'مميز',
            value: 'premium',
        },
    };


    return {
        cases,
        enumData
    }

}