export default function NotificationTypeEnum() {

    const cases = {
        ONE: 'one',
        GROUP: 'group',
        FREE: 'free',
        PREMIUM: 'premium',
        ALL: 'all',
    };

    const enumData = {
        one: {
            title_en: 'Client',
            title_ar: 'مستخدم',
            value: 'one',
        },
        group: {
            title_en: 'Group',
            title_ar: 'مجموعة',
            value: 'group',
        },
        free: {
            title_en: 'Free',
            title_ar: 'مجاني',
            value: 'free',
        },
        premium: {
            title_en: 'Premium',
            title_ar: 'مميز',
            value: 'premium',
        },
        all: {
            title_en: 'All',
            title_ar: 'الكل',
            value: 'all',
        },
    };

    return {
        cases,
        enumData
    }
}
