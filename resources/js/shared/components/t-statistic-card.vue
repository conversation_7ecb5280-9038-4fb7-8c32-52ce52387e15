

<template>
    <v-card
        class="mb-4"
        elevation="4"
        variant="elevated"
        :color="color"
        title-class="text-white"
        :loading="loading"
    >
        <v-card-title class="text-wrap">
            <h6>
                <v-icon>{{ iconPrefix }}{{ icon }}</v-icon>
                {{ title }}
            </h6>
        </v-card-title>
        <v-card-text class="text-center">
            <div :class="valueClass">
                <slot>{{ value }}</slot>
            </div>
        </v-card-text>
    </v-card>
</template>

<script setup>
import { defineProps } from "vue";

const props = defineProps({
    title: {
        type: String,
        required: true
    },
    icon: {
        type: String,
        required: true
    },
    iconPrefix: {
        type: String,
        default: "mdi mdi-"
    },
    value: {
        type: [String, Number],
        default: ""
    },
    color: {
        type: String,
        default: "white"
    },
    valueClass: {
        type: String,
        default: "text-h4 font-weight-bold"
    },
    loading: {
        type: Boolean,
        default: false
    }
});
</script>

<style lang="css" scoped></style>
