<template>
    <v-navigation-drawer
        class="spacing-playground pa-2"
        color="white"
        width="312"
        v-model="drawer"
        :rail="rail"
        @click="rail = false"
    >
        <v-list density="compact" nav class="py-0">
            <v-list-item
                class="white-image"
                v-if="$store.state.auth.status.loggedIn"
                to="/profile"
            >
                <div class="colored-btn profile-icon">
                    <img src="@/assets/icons/ic_profile.svg" />
                </div>
                <span class="user-name px-3 text-shades-black">
                    {{ currentUser.first_name }}</span
                >
            </v-list-item>
            <v-divider :thickness="2" class="mb-3 mt-2"></v-divider>

            <!-- Dashboard -->
            <v-list-item v-if="$store.state.auth.status.loggedIn" to="/">
                <img src="@/assets/icons/ic_dashboard.svg" />
                <span class="px-3"> {{ $t("navigation.dashboard") }}</span>
            </v-list-item>

            <!-- statistics -->
            <v-list-group
                v-if="
                    currentUser.permissions.includes('statistics/clients')
                "
                value="statistics"
            >
                <template v-slot:activator="{ props }">
                    <v-list-item v-bind="props">
                        <v-icon icon="mdi mdi-chart-arc"></v-icon>
                        <span class="px-3">
                            {{ $t("navigation.statistics") }}</span
                        >
                    </v-list-item>
                </template>

                <!-- clients -->
                <v-list-item
                    v-if="currentUser.permissions.includes('statistics/clients')"
                    to="/statistics/clients"
                >
                <v-icon icon="mdi mdi-chart-arc"></v-icon>
                    <span class="px-3">
                        {{ $t("navigation.clients_statistics") }}</span
                    >
                </v-list-item>

                <!-- Reports -->
            <v-list-group
                v-if="currentUser.permissions.includes('reports')"
                value="reports"
            >
                <template v-slot:activator="{ props }">
                    <v-list-item v-bind="props">
                        <img src="@/assets/icons/ic_reports.svg" />
                        <span class="px-3">
                            {{ $t("navigation.reports") }}</span
                        >
                    </v-list-item>
                </template>
                <v-list-item
                    v-if="currentUser.permissions.includes('reports')"
                    to="/reports/servers"
                >
                    <span class="px-3"> {{ $t("navigation.servers") }}</span>
                </v-list-item>
            </v-list-group>

            </v-list-group>

            <!-- Clients -->
            <v-list-item
                v-if="currentUser.permissions.includes('clients')"
                to="/clients"
            >
                <img src="@/assets/icons/ic_Customer_management.svg" />
                <span class="px-3"> {{ $t("navigation.clients") }}</span>
            </v-list-item>

            <!-- Infrastructure -->
            <v-list-group
                v-if="
                    currentUser.permissions.includes('servers') ||
                    currentUser.permissions.includes('protocols') ||
                    currentUser.permissions.includes('locations') ||
                    currentUser.permissions.includes('providers')
                "
                value="infrastructure"
            >
                <template v-slot:activator="{ props }">
                    <v-list-item v-bind="props">
                        <img src="@/assets/icons/ic_dashboard.svg" />
                        <span class="px-3">
                            {{ $t("navigation.infrastructure") }}</span
                        >
                    </v-list-item>
                </template>

                <!-- Servers -->
                <v-list-item
                    v-if="currentUser.permissions.includes('servers')"
                    to="/servers"
                >
                    <span class="px-3"> {{ $t("navigation.servers") }}</span>
                </v-list-item>

                <!-- Protocols -->
                <v-list-item
                    v-if="currentUser.permissions.includes('protocols')"
                    to="/protocols"
                >
                    <span class="px-3"> {{ $t("navigation.protocols") }}</span>
                </v-list-item>

                <!-- Locations -->
                <v-list-item
                    v-if="currentUser.permissions.includes('locations')"
                    to="/locations"
                >
                    <span class="px-3"> {{ $t("navigation.locations") }}</span>
                </v-list-item>

                <!-- Providers -->
                <v-list-item
                    v-if="currentUser.permissions.includes('providers')"
                    to="/providers"
                >
                    <span class="px-3"> {{ $t("navigation.providers") }}</span>
                </v-list-item>
            </v-list-group>

            <!-- Packages -->
            <v-list-item
                v-if="currentUser.permissions.includes('packages')"
                to="/packages"
            >
                <img src="@/assets/icons/ic_dashboard.svg" />
                <span class="px-3"> {{ $t("navigation.packages") }}</span>
            </v-list-item>

            <!-- Notifications -->
            <v-list-group
                v-if="
                    currentUser.permissions.includes('notifications') ||
                    currentUser.permissions.includes('groups')
                "
                value="notifications"
            >
                <template v-slot:activator="{ props }">
                    <v-list-item v-bind="props">
                        <img src="@/assets/icons/ic_notifications.svg" />
                        <span class="px-3">
                            {{ $t("navigation.notifications") }}</span
                        >
                    </v-list-item>
                </template>
                <v-list-item
                    v-if="currentUser.permissions.includes('notifications')"
                    to="/notifications"
                >
                    <span class="px-3">
                        {{ $t("navigation.notifications") }}</span
                    >
                </v-list-item>
                <v-list-item
                    v-if="currentUser.permissions.includes('groups')"
                    to="/groups"
                >
                    <span class="px-3">{{ $t("navigation.groups") }}</span>
                </v-list-item>
            </v-list-group>

            <!-- Tickets -->
            <v-list-item
                v-if="currentUser.permissions.includes('tickets')"
                to="/tickets"
            >
                <img src="@/assets/icons/ic_dashboard.svg" />
                <span class="px-3"> {{ $t("navigation.tickets") }}</span>
            </v-list-item>

            <!-- Expenses -->
            <v-list-item
                v-if="currentUser.permissions.includes('expenses')"
                to="/expenses"
            >
                <img src="@/assets/icons/ic_dashboard.svg" />
                <span class="px-3"> {{ $t("navigation.expenses") }}</span>
            </v-list-item>

            <v-list-item
                    v-if="
                        currentUser.permissions.includes(
                            'settings/indexContent'
                        )
                    "
                    to="/index-content"
                >
                <v-icon icon="mdi mdi-table-of-contents"></v-icon>
                    <span class="px-3"> {{ $t("navigation.contents") }}</span>
                </v-list-item>

            <!-- Users & Roles -->
            <v-list-group
                v-if="
                    currentUser.permissions.includes('users') ||
                    currentUser.permissions.includes('roles')
                "
                value="users_management"
            >
                <template v-slot:activator="{ props }">
                    <v-list-item v-bind="props">
                        <img src="@/assets/icons/ic_Customer_management.svg" />
                        <span class="px-3">
                            {{ $t("navigation.users_roles") }}</span
                        >
                    </v-list-item>
                </template>
                <v-list-item
                    v-if="currentUser.permissions.includes('users')"
                    to="/users"
                >
                    <span class="px-3"> {{ $t("navigation.users") }}</span>
                </v-list-item>
                <v-list-item
                    v-if="currentUser.permissions.includes('roles')"
                    to="/roles"
                >
                    <span class="px-3"> {{ $t("navigation.roles") }}</span>
                </v-list-item>
            </v-list-group>

            <!-- Settings -->
            <!-- <v-list-group
                v-if="currentUser.permissions.includes('settings')"
                value="settings"
            >
                <template v-slot:activator="{ props }">
                    <v-list-item v-bind="props">
                        <img src="@/assets/icons/ic_setting.svg" />
                        <span class="px-3">
                            {{ $t("navigation.settings") }}</span
                        >
                    </v-list-item>
                </template>
                
            </v-list-group> -->

            <v-list-item
                    v-if="currentUser.permissions.includes('settings')"
                    to="/settings"
                >
                    <v-icon icon="mdi mdi-card-bulleted-settings"></v-icon>
                    <span class="px-3"> {{ $t("navigation.settings") }}</span>
                </v-list-item>

            
        </v-list>
    </v-navigation-drawer>
</template>

<script setup>
import { ref } from "vue";
import store from "../../store/store.js";

const currentUser = ref(store.state.auth.user);

const drawer = defineModel("drawer", { type: Boolean });
const rail = ref(false);
</script>
