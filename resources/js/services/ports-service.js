import BaseService from "./base-service";
import axios from "axios";
import authHeader from "./auth-header.js";

class PortsService extends BaseService {
    routPath = '/ports';

    constructor() {
        super();
    }


        protocols() {
            return axios.get(this.routPath + '-protocols'
                , {headers: authHeader()}
            );
        }
}

export default new PortsService();
