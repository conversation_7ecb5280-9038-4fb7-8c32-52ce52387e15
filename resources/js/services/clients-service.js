import BaseService from "./base-service";
import axios from "axios";
import authHeader from "./auth-header";

class ClientsService extends BaseService {
    routPath = '/clients';

    constructor() {
        super();
    }

    devices(id, params, showLoader) {
        return axios.post(this.routPath + '-devices?page=' + params.page +
            "&limit=" + params.size + "&search=" + params.search+ "&sort=" + params.sort
            , { id: id },{headers: authHeader(), showLoader: showLoader}
        );
    }

    sessions(id, params, showLoader) {
        return axios.post(this.routPath + '-sessions?page=' + params.page +
            "&limit=" + params.size + "&search=" + params.search+ "&sort=" + params.sort
            , { id: id },{headers: authHeader(), showLoader: showLoader}
        );
    }

}

export default new ClientsService();
