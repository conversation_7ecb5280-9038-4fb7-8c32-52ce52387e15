import BaseService from "../base-service";
import axios from "axios";
import authHeader from "../auth-header";

class ClientNotificationsService extends BaseService {
    routPath = '/client-notifications';

    constructor() {
        super();
    }


    index(params, showLoader) {
        return axios.get(this.routPath + '?notification_id=' + params.notification_id +
            '&page=' + params.page +
            "&limit=" + params.size +
            "&search=" + params.search
            , {headers: authHeader(), showLoader}
        );
    }
}

export default new ClientNotificationsService();
