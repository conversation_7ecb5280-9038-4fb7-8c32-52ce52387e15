import BaseService from "./base-service";
import authHeader from "@/services/auth-header";
import axios from "axios";


class SettingsService extends BaseService {
    routPath = '/settings';

    constructor() {
        super();
    }

    index(params, showLoader) {
        return axios.get(this.routPath + '?parent_id=' + params.parent_id +
            '&page=' + params.page +
            "&limit=" + params.size +
            "&search=" + params.search +
            "&sort=" + params.sort
            , {headers: authHeader(), showLoader}
        );
    }

    indexContent(params, showLoader) {
        return axios.get('index-content' + '?parent_id=' + params.parent_id +
            '&page=' + params.page +
            "&limit=" + params.size +
            "&search=" + params.search +
            "&sort=" + params.sort,
            {headers: authHeader(), showLoader}
        );
    }
}

export default new SettingsService();
