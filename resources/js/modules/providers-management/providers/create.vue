<template>
    <v-container>
        <v-card width="90%" class="pa-5 mx-auto" color="white" outlined>
            <v-card-title class="headline black" primary-title>
                <h3>
                    {{ $t('providers.add') }}
                </h3>
            </v-card-title>
            <v-card-text class="pa-5">
                <v-form
                    v-model="valid"
                    v-on:submit.prevent="emit('create', {...createForm}, 'providers', true)"
                >
                    <v-row class="mt-n2">
                        <v-col cols="12" md="6" lg="6">
                            <v-text-field
                                v-model="createForm.name"
                                :label="$t('providers.name')"
                                variant="outlined"
                                :rules="validation.name"
                                class="required"
                            >
                            </v-text-field>
                        </v-col>
                        <v-col cols="12" md="6" lg="6">
                            <v-text-field
                                v-model="createForm.contact_number"
                                :label="$t('providers.contact_number')"
                                variant="outlined"
                                :rules="validation.contact_number"
                            >
                            </v-text-field>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" md="6" lg="6">
                            <v-text-field
                                v-model="createForm.email"
                                :label="$t('providers.email')"
                                variant="outlined"
                                :rules="validation.email"
                            >
                            </v-text-field>
                        </v-col>
                        <v-col cols="12" md="6" lg="6">
                            <v-text-field
                                v-model="createForm.website"
                                :label="$t('providers.website')"
                                variant="outlined"
                                :rules="validation.website"
                            >
                            </v-text-field>
                        </v-col>
                    </v-row>
                    <v-row>
                        <v-col cols="12" md="6" lg="6">
                            <v-text-field
                                v-model="createForm.admin_url"
                                :label="$t('providers.admin_url')"
                                variant="outlined"
                                :rules="validation.admin_url"
                            >
                            </v-text-field>
                        </v-col>
                        <v-col cols="12" md="6" lg="6">
                            <v-textarea
                                v-model="createForm.note"
                                :label="$t('providers.note')"
                                variant="outlined"
                                :rules="validation.note"
                                rows="3"
                            >
                            </v-textarea>
                        </v-col>
                    </v-row>
                    <v-btn
                        @click="emit('cancel')"
                        :class="'float-' + $t('right')"
                        class="colored-btn-cancel"
                    >
                        {{ $t("cancel") }}
                    </v-btn>
                    <v-btn
                        :class="'float-' + $t('right') + ' colored-btn'"
                        type="submit"
                    >
                        <span class="px-2">{{ $t("save") }}</span>
                    </v-btn>
                </v-form>
            </v-card-text>
        </v-card>
    </v-container>
</template>
<script setup>
import { ref } from 'vue';

const valid = defineModel("valid", {type: Boolean});
const form = defineModel("form", {type: Object});
const props = defineProps(["validation"]);
const emit = defineEmits(["cancel", "create"]);

const createForm = ref({...form.value});
</script>

