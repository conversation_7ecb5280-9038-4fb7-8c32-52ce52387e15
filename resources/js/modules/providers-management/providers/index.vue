<template>
    <t-modal v-model:show="storeModal" :width="'70%'">
        <create-provider
            v-model:form="form"
            v-model:valid="valid"
            :validation="validation"
            @create="storeModalItem"
            @cancel="cancel()"
        />
    </t-modal>

    <t-modal v-model:show="updateModal" :width="'70%'">
        <update-provider
            v-model:form="itemData"
            v-model:valid="valid"
            :validation="validation"
            @update="updateModalItem"
            @cancel="cancel()"
        />
    </t-modal>

    <v-container>
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="itemData?.name_ar ?? $t(router.currentRoute.value.meta.breadcrumb)"
            :reset="parent ? false : true"
        >
        </t-breadcrumbs>
    </v-container>
    <v-container>
        <h3>{{ $t('providers.providers') }}</h3>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>
        <v-btn
            v-if="userPermissions.includes('providers/create')"
            @click="showStoreModal()" :class="'float-'+$t('right') + ' colored-btn'" >
            <span class="px-2">{{ $t('providers.add') }}</span>
            <img class="crud-icon" src="@/assets/icons/ic_add_2.svg">
        </v-btn>
        <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
            <t-data-table
                :rows="tableData"
                :pagination="pagination"
                :query="query"
                :queryType="'LoadData'"
                :loading="isLoading"
                :userPermissions="userPermissions"
                :cols="providerCols"
                :actions="providerActions"
                @loadData="loadData"
            >
            </t-data-table>
        </div>
    </v-container>
</template>

<script setup>

import UpdateProvider from "@/modules/providers-management/providers/update.vue";
import CreateProvider  from "@/modules/providers-management/providers/create.vue";
import TDataTable from "@/shared/components/t-data-table.vue";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import TModal from "@/shared/components/t-modal.vue";
import useProvider from "@/modules/providers-management/composables/provider.js";

const {
    parent,
    tableData,
    pagination,
    query,
    isLoading,
    updateModal,
    storeModal,
    cancel,
    itemData,
    showStoreModal,
    storeModalItem,
    updateModalItem,
    loadParentData,
    loadData,
    router,
    userPermissions,
    addressCols,
    addressActions,
    valid,
    validation,
    form,
    service,
    address,
    getItem,
    providerActions,
    providerCols,
} = useProvider()



</script>

