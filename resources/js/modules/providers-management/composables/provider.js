import {reactive, ref} from 'vue'
import ProviderService from "@/services/provider-management-services/provider-service.js";
import useShared from "@/helpers/shared.js";
import providerTableItems from "@/modules/providers-management/models/provider-table-item";

export default function useProvider() {

    const {
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        itemData,
        getItem,
        parent,
        storeItem,
        updateItem,
        parentDetails,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        loadData,
        loadParentData,
        deleteItem,
        cancel,
        saveItem,
        router,
        userPermissions,
        t,
        redirect
    } = useShared()

    service.value = ProviderService;

    const provider = ref()

    const {
        cols: providerCols,
        actions: providerActions
    } = providerTableItems(t, redirect, showUpdateModal, deleteItem);

    const form = reactive({
        name: "",
        contact_number: "",
        email: "",
        website: "",
        admin_url: "",
        note: "",
    });

    const validation = {
        name: [
            validationRules.required,
            validationRules.maxLength(50),
        ],
        contact_number: [
            validationRules.phone,
            validationRules.optional,
            validationRules.maxLength(50),
        ],
        email: [
            validationRules.optional,
            validationRules.email,
            validationRules.maxLength(100),
        ],
        website: [
            validationRules.optional,
            validationRules.url,
            validationRules.maxLength(255),
        ],
        admin_url: [
            validationRules.optional,
            validationRules.url,
            validationRules.maxLength(255),
        ],
        note: [
            validationRules.optional,
        ],
    }


    return {
        itemData,
        tableData,
        pagination,
        query,
        provider,
        isLoading,
        validation,
        valid,
        parent,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        getItem,
        loadData,
        service,
        loadParentData,
        storeItem,
        parentDetails,
        updateItem,
        deleteItem,
        saveItem,
        cancel,
        router,
        userPermissions,
        providerCols,
        providerActions,
        form
    }
}
