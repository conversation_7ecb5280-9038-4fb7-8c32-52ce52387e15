
export default function serverTableItems(t, redirect, showUpdateModal) {

    const cols = [
        { header: 'Name', field: 'reports.servers.name', cell: (item) => item.name },
        { header: 'Status', field: 'reports.servers.status', cell: (item) => item.status['title_' + t('locale.lang')] },
        { header: 'CPU', field: 'reports.servers.cpu', cell: (item) => item.cpu ?? '-' },
        { header: 'RAM', field: 'reports.servers.ram', cell: (item) => item.ram ?? '-' },
        { header: 'DISK ', field: 'reports.servers.disk', cell: (item) => item.disk ?? '-' },
        { header: 'TCPUsers ', field: 'reports.servers.tcp_users', cell: (item) => item.tcp_users ?? '-' },
        { header: 'UDPUsers', field: 'reports.servers.udp_users', cell: (item) => item.udp_users ?? '-' },
        { header: 'WireGuardUsers', field: 'reports.servers.wireguard_users', cell: (item) => item.wireguard_users ?? '-' },
        { header: 'TotalUsers', field: 'reports.servers.total_users', cell: (item) => item.total_users ?? '-' }
    ];

    const actions = [];

    return {
        cols
    }
}
