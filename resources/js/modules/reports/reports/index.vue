<template>
    <t-modal v-model:show="updateModal" :width="'70%'">
        <update-setting
            v-model:form="itemData"
            v-model:valid="valid"
            :validation="validation"
            @update="updateModalItem"
            @cancel="cancel()"
        />
    </t-modal>

    <v-container>
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="itemData?.name_ar ?? $t(router.currentRoute.value.meta.breadcrumb)"
            :reset="parent ? false : true"
        >
        </t-breadcrumbs>
    </v-container>
    <v-container>
        <h3>{{ $t('reports.reports') }}</h3>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>
        <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
            <t-data-table
                :rows="tableData"
                :pagination="pagination"
                :query="query"
                :queryType="'LoadData'"
                :loading="isLoading"
                :userPermissions="userPermissions"
                :cols="settingCols"
                :actions="settingActions"
                @loadData="loadData"
            >
            </t-data-table>
        </div>
    </v-container>
</template>

<script setup>

import UpdateSetting from "@/modules/reports/reports/update.vue";
import TDataTable from "@/shared/components/t-data-table.vue";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import TModal from "@/shared/components/t-modal.vue";
import useReports from "@/modules/reports/composables/reports";


const {
    parent,
    tableData,
    pagination,
    query,
    isLoading,
    updateModal,
    storeModal,
    cancel,
    itemData,
    settingActions,
    settingCols,
    updateModalItem,
    loadParentData,
    loadData,
    router,
    userPermissions,
    addressCols,
    addressActions,
    valid,
    validation,
    form,
    service,
    setting,
    getItem,
} = useReports()


</script>
