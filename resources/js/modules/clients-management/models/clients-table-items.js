import clientStatusEnum from '@/enums/client-status-enum';
import clientTypesEnum from '@/enums/client-types-enum';

export default function clientTableItems(t, deleteItem, redirect) {

        const {
            cases: clientStatusCases,
            enumData: clientStatusEnumData
        } = clientStatusEnum();
    
        const {
            cases: clientTypesCases,
            enumData: clientTypesEnumData
        } = clientTypesEnum();
    

    const cols = [
        { header: 'id', field: 'clients.id', cell: (item) => item.id },
        { header: 'username', field: 'clients.username', cell: (item) => item.username },
        { header: 'email', field: 'clients.email', cell: (item) => item.email },
        { header: 'status', field: 'clients.status', cell: (item) => clientStatusEnumData[item.status][`title_${t("locale.lang")}`] },
        { header: 'type', field: 'clients.type', cell: (item) => clientTypesEnumData[item.type][`title_${t("locale.lang")}`] },
        { header: 'registered_at', field: 'clients.registered_at', cell: (item) => item.registered_at },
        { header: 'last_connection', field: 'clients.last_connection', cell: (item) => item.last_used_at },

    ];

    const actions = [
        {
            header: 'details',
            perm: 'clients/details',
            class: 'crud-action-btn',
            icon: "mdi-eye",
            action: (item) => redirect('clients/details', { id: item.id })
        },
        {
            header: 'update',
            perm: 'clients/update',
            icon: "mdi-pencil",
            action: (item) => redirect('clients/update', { id: item.id })
        },
        {
            header: 'delete',
            perm: 'clients/delete',
            icon: "mdi-delete",
            action: (item) => deleteItem(item.id)
        }
    ];


    return {
        cols,
        actions
    }
}