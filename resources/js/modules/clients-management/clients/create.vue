<template>
    <v-container>
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="$t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>

    <v-container class="font-weight-bold">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("clients.add") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>

        
        <v-form
                v-model="valid"
                @submit.prevent="save"
            >
        <v-row>
            
            <v-col cols="12" md="6" lg="6">
                <v-text-field
                    dense
                    type="email"
                    v-model="form.email"
                    class="required"
                    :label="$t('clients.email')"
                    variant="outlined"
                    :rules="validation.email"
                >
                </v-text-field>
            </v-col>
            
            <v-col cols="12" md="6" lg="6">
                <v-text-field
                    dense
                    type="password"
                    class="required"
                    v-model="form.password"
                    :label="$t('clients.password')"
                    variant="outlined"
                    :rules="[ validationRules.required ,...validation.password]"
                >
                </v-text-field>
            </v-col>

        </v-row>

        <router-link :to="{ name: 'clients' }">
                <v-btn
                    :class="'float-' + $t('right')"
                    class="colored-btn-cancel"
                >
                    {{ $t("cancel") }}
                </v-btn>
            </router-link>
            <v-btn
                :class="'float-' + $t('right') + ' colored-btn'"
                type="submit"
            >
                <span class="px-2">{{ $t("save") }}</span>
                <img class="crud-icon" src="@/assets/icons/ic_add_2.svg" />
            </v-btn>

        </v-form>
    </v-container>
</template>
<script setup>
import useClients from "../composables/clients.js";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";

const {
    validationRules,
    isLoading,
    storeItem,
    validation,
    form,
    valid,
    router,

    notify,
    t,
    userPermissions,
} = useClients();


const save = async () => {
    await storeItem({ ...form }, 'clients')
}


</script>
