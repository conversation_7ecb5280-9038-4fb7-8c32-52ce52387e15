<template>
    <div>
        <v-row>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("clients.username") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ itemData.username }}
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("clients.email") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ itemData.email }}
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("clients.type") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ clientTypesEnumData[itemData.type][`title_${$t("locale.lang")}`] }}
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("clients.status") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ clientStatusEnumData[itemData.status][`title_${$t("locale.lang")}`] }}
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("clients.is_verified") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ itemData.is_verified ? $t("yes") : $t("no") }}
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("clients.registered_at") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ itemData.registered_date }}
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("clients.last_connection") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ itemData?.last_access_token?.last_used_at_formatted }}
                </b>
            </v-col>
        </v-row>

    </div>
</template>

<script setup>
import { onMounted } from 'vue';
import useClients from "../../composables/clients.js";
const {
    clientTypesEnumData,
    clientStatusEnumData,
} = useClients();

const props = defineProps({
    itemData: {
        required: true,
        type: Object
    }
})

onMounted(() => {
})
</script>
