<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="itemData.name ?? $t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>

    <v-container v-if="!isLoading" class="font-weight-bold">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("clients.details") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>

        <main-data :itemData="itemData" />
        <details-tabs :itemData="itemData" />

    </v-container>
</template>
<script setup>
import useClients from "../composables/clients.js";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import {onMounted} from "vue";
import MainData from "./details/main.vue";
import DetailsTabs from "./details/tabs.vue";

const {
    isLoading,
    itemData,
    getItem,
    router,
} = useClients();

const props = defineProps({
    id: {
        required: true,
        type: String
    }
})

onMounted(() => {
    getItem(props.id, true)
})


</script>
