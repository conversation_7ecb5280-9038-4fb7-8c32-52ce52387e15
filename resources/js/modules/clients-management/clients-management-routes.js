import ClientIndex from "@/modules/clients-management/clients/index.vue";
import ClientCreate from "@/modules/clients-management/clients/create.vue";
import ClientUpdate from "@/modules/clients-management/clients/update.vue";
import ClientDetails from "@/modules/clients-management/clients/details.vue";

const ClientsManagementRoutes = [
    {
        path: '/clients',
        name: 'clients',
        component: ClientIndex,
        meta: {
            breadcrumb: 'clients.clients'
        }
    },
    {
        path: '/clients/create',
        name: 'clients/create',
        component: ClientCreate,
        props: true,
        meta: {
            breadcrumb: 'clients.add'
        }
    },
    {
        path: "/clients/:id/update",
        name: 'clients/update',
        component: ClientUpdate,
        props: true,
        meta: {
            breadcrumb: 'clients.edit'
        }
    },
    {
        path: "/clients/:id/details",
        name: 'clients/details',
        component: ClientDetails,
        props: true,
        meta: {
            breadcrumb: 'clients.details'
        }
    },
    

]

export default ClientsManagementRoutes;
