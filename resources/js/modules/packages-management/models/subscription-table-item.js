
import SubscriptionStatusEnum from "@/enums/subscription-status-enum";

export default function SubscriptionsTableItems(t, deleteItem, redirect, disableSubscription) {
    const { enumData, cases } = SubscriptionStatusEnum();

    const cols = [
        { header:'clients.user_name.client_id', field: 'subscriptions.user_name', cell: (item) => item.user_name },
        { header:'clients.email.client_id', field: 'subscriptions.email', cell: (item) => item.email },
        { header:'from', field: 'subscriptions.from', cell: (item) => item.from },
        { header:'to', field: 'subscriptions.to', cell: (item) => item.to },
        { header:'status', field: 'subscriptions.status', cell: (item) => enumData[item.status]?.[`title_${t('locale.lang')}`] || item.status },
        { header:'is_paid', field: 'subscriptions.is_paid', cell: (item) =>  t(item.is_paid ? 'yes' : 'no') },

    ];

    const actions = [
        {
            header: 'disable',
            perm: 'subscriptions/disable',
            icon: "mdi-stop",
            action: (item) => disableSubscription(item.id),
            visible: (item) => item.status === cases.ACTIVE
        }
    ];


    return {
        cols,
        actions
    }
}
