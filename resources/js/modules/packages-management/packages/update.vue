<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="$t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>
    <v-container v-if="!isLoading">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t('packages.edit') }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>
        <v-form v-model="valid" v-on:submit.prevent="savePackage">
            <v-row class="mt-n5">
                
                <v-col cols="12" md="6" lg="6">
                    <v-text-field
                        v-model="itemData.name"
                        :label="$t('packages.name')"
                        variant="solo"
                        :rules="validation.name"
                        class="required"
                    >
                    </v-text-field>
                </v-col>


            
                <v-col cols="12" md="6" lg="6">
                    <v-text-field
                        v-model="itemData.price"
                        :label="$t('packages.price')"
                        variant="solo"
                        :rules="validation.price"
                        class="required"
                        suffix="$"
                    >
                    </v-text-field>
                </v-col>
            

            
                <v-col cols="12" md="6" lg="6">
                    <v-text-field
                        v-model="itemData.duration"
                        :label="$t('packages.duration')"
                        variant="solo"
                        :rules="validation.duration"
                        :type="'number'"
                        class="required"
                    >
                    </v-text-field>
                </v-col>
            

            
                <v-col cols="12" md="6" lg="6">
                    <v-select
                        :label="$t('packages.unit')"
                        v-model="itemData.unit"
                        :items="unitStatus"
                        :item-title="item => $t(`locale.lang`) === 'en' ? item.title_en : item.title_ar"
                        item-value="value"
                        variant="solo"
                        clearable

                    >
                    </v-select>
                </v-col>
            

            
                <v-col cols="12" md="6" lg="6">
                    <v-select
                        :label="$t('packages.status')"
                        v-model="itemData.status"
                        :items="packageStatus"
                        :item-title="item => $t(`locale.lang`) === 'en' ? item.title_en : item.title_ar"
                        item-value="value"
                        variant="solo"
                        clearable

                    >
                    </v-select>
                </v-col>
            </v-row>

            <router-link :to="{ name: 'packages'}">
                <v-btn :class=" 'float-'+$t('right') " class="colored-btn-cancel">
                    {{ $t('cancel') }}
                </v-btn>
            </router-link>
            <v-btn :class="'float-'+$t('right') + ' colored-btn'"
                   type="submit"
            >
                <span class="px-2">{{ $t('edit') }}</span>
            </v-btn>
        </v-form>

    </v-container>
</template>
<script setup>
import {onMounted, ref} from 'vue';
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import usePackages from "@/modules/packages-management/composables/packages";
import PackageStatusEnum from "@/enums/package-status-enum";
import PackageUnitEnum from "@/enums/package-unit-enum";


const {
    router,
    isLoading,
    form,
    itemData,
    validation,
    valid,
    storeItem,
    getItem,
    updateItem,
} = usePackages()

const packageStatus = ref([]);
const unitStatus = ref([]);

const getPackageStatus = () => {
    const { enumData } = PackageStatusEnum();
    packageStatus.value = Object.values(enumData);
};

const getUnitStatus = () => {
    const { enumData } = PackageUnitEnum();
    unitStatus.value = Object.values(enumData);
};

const props = defineProps({
    id: {
        required: true,
        type: String
    }
})

onMounted( () => {
    getUnitStatus();
    getPackageStatus();
    getItem(props.id, true)
});


const savePackage = async () => {
    await updateItem(itemData.value, 'packages', true)
}

</script>

