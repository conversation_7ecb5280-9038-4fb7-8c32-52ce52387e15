
<template>
    <v-row class="mt-4">
        <v-col>

            <v-tabs
                v-model="tabs"
                bg-color="primary"
            >
                <v-tab value="subscriptions">{{ $t('packages.subscriptions') }}</v-tab>
                <v-tab value="payments">{{ $t('packages.payments') }}</v-tab>

            </v-tabs>

            <v-card-text>
                <v-tabs-window v-model="tabs">
                    <v-tabs-window-item value="subscriptions">
                        <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
                            <t-data-table
                            v-if="itemData"
                                :rows="Subscriptions"
                                :pagination="subscriptionPagination"
                                :query="subscriptionQuery"
                                :loading="SubscriptionsIsLoading"
                                :queryType="`Load${props.itemData.id}SubscriptionsData`"
                                :userPermissions="userPermissions"
                                :cols="SubscriptionsCols"
                                :actions="SubscriptionsActions"
                                @loadData="getSubscriptions"
                            >
                            </t-data-table>
                        </div>
                    </v-tabs-window-item>

                    <v-tabs-window-item value="payments">
                        payments
                    </v-tabs-window-item>

                </v-tabs-window>
            </v-card-text>
        </v-col>

    </v-row>

</template>

<script setup>
import { onMounted } from 'vue';
import TDataTable from "@/shared/components/t-data-table.vue";
import usePackages from "@/modules/packages-management/composables/packages";
import SubscriptionsTableItems from "@/modules/packages-management/models/subscription-table-item";

const props = defineProps({
    itemData: {
        required: true,
        type: Object
    }
})

const {
    tabs,
    subscriptionPagination,
    subscriptionQuery,
    Subscriptions,
    SubscriptionsIsLoading,
    getSubscriptions,
    deleteItem,
    disableSubscription,
    itemData,
    t,
    redirect,
    userPermissions,
} = usePackages();

const {
    cols: SubscriptionsCols,
    actions: SubscriptionsActions
} = SubscriptionsTableItems(t, deleteItem, redirect, disableSubscription);

onMounted(async () => {
    itemData.value = props.itemData;
});
</script>
