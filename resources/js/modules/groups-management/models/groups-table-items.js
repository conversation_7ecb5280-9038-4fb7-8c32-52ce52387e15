export default function groupTableItems(t, deleteItem, redirect) {

    const cols = [
        { header: 'name', field: 'groups.name', cell: (item) => item.name },

    ];

    const actions = [
        {
            header: 'details',
            perm: 'groups/details',
            class: 'crud-action-btn',
            icon: "mdi-eye",
            action: (item) => redirect('groups/details', { id: item.id })
        },
        {
            header: 'update',
            perm: 'groups/update',
            icon: "mdi-pencil",
            action: (item) => redirect('groups/update', { id: item.id })
        },
        {
            header: 'delete',
            perm: 'groups/delete',
            icon: "mdi-delete",
            action: (item) => deleteItem(item.id)
        }
    ];


    return {
        cols,
        actions
    }
}