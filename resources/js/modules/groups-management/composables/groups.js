import {reactive, ref} from 'vue'
import GroupService from "@/services/groups-service.js";
import useShared from "@/helpers/shared.js";
import groupTableItems from '../models/groups-table-items';
import clientsSelectTableItems from '../models/clients-select-table-items';
import cookie from "vue-cookies";

export default function useGroups() {

    const {
        notify,
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        itemData,
        getItem,
        parent,
        storeItem,
        updateItem,
        parentDetails,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        loadParentData,
        deleteItem,
        cancel,
        loadData,
        saveItem,
        router,
        userPermissions,
        t,
        errorHandle,
        createConfirmDialog,
        ConfirmDialog,
        redirect
    } = useShared()

    service.value = GroupService;

    const isLoadingTable = ref(true)
    const clientsTableCookies = 'LoadParentData';

    const {
        cols: groupCols,
        actions: groupActions
    } = groupTableItems(t, deleteItem, redirect);



    const form = reactive({
        name: '',
        selectAllFilterValue: '',
        clients: [],
    });

    const validation = {
        name: [
            validationRules.required,
        ],
        clients: [
            validationRules.required,
        ],
    }
    const clients = ref([])


    const SelectAllFilteredClients = reactive({
        active: 0,
        queryValue:'',
        show: 0
    });
    
    const loadGroupClients = async (query, parent_name = '') => {
        try {
            if (query === undefined)
                query = {
                    search: '',
                    page: 0,
                    per_page: 10,
                }

            const {data: {data, meta, parentData}} = await service.value.groupClients({
                parent_id: (parent.value !== null) ? parent.value : '',
                parent_name: parent_name,
                page: query.page,
                size: query.per_page,
                search: query.search,
            });

            tableData.value = data
            if (parentData)
                parentDetails.value = parentData;

            pagination.value = {...pagination.value, page: query.page, total: meta.total}
            cookie.set(`${service.value.routPath}${clientsTableCookies + parent.value}`,
                 JSON.stringify({pagination: pagination.value, query: query}));
            isLoading.value = false
        } catch (error) {
            await errorHandle(error)
        }
    }


    const detachClient = async (id) => {
        const dialog = createConfirmDialog(ConfirmDialog);
        
        dialog.onConfirm(async () => {
            try {
                let response = await service.value.detachClient({
                    client_id: id,
                    group_id: itemData.value.id,
                });
                notify(response.data.message);
                loadGroupClients();
            } catch (error) {
                await errorHandle(error);
            }
        });
        await dialog.reveal();
    };

      
    const {
        cols: clientCols,
        actions: clientActions
    } = clientsSelectTableItems(t, detachClient);

    
    const loadClients = async (query) => {
                try {
                    isLoadingTable.value = true;
                    if (query === undefined)
                        query = {
                            search: '',
                            page: 1,
                            sort:'',
                            per_page: 10,
                        }
                        SelectAllFilteredClients.show = query.search ? 1 : 0;
                        SelectAllFilteredClients.active = query.search ? 1 : 0;
                        SelectAllFilteredClients.queryValue = query.search;
                    const {data: {data, meta}} = await service.value.clients({
                        parent_id: '',
                        page: query.page,
                        sort: query.sort,
                        size: query.per_page,
                        search: query.search,
                    }, false);
                    clients.value = data;
                    pagination.value = {...pagination.value, page: query.page, total: meta.total}

                    isLoadingTable.value = false
                } catch (error) {
                    isLoadingTable.value = false
                    await errorHandle(error)
                }
    }



    const rowClickHandler = (row) => {
        let selectedItems = itemData.value ? itemData.value.clients_ids : form.clients;
        const index = selectedItems.findIndex((item) => item === row.id);

        if (index !== -1) {
            selectedItems.splice(index, 1);
        } else {
            selectedItems.push(row.id);
        }
    };


    return {
        loadGroupClients,
        SelectAllFilteredClients,
        rowClickHandler,
        isLoadingTable,
        loadClients,
        clients,
        clientCols,
        clientActions,

        itemData,
        tableData,
        pagination,
        query,
        isLoading,
        loadData,
        validation,
        form,
        valid,
        parent,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        getItem,
        loadParentData,
        storeItem,
        parentDetails,
        updateItem,
        deleteItem,
        saveItem,
        cancel,
        router,
        userPermissions,
        groupCols,
        groupActions,
        form
    }
}
