<template>
    <v-container>
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="$t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>

    <v-container class="font-weight-bold">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("groups.add") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>

        <v-form v-model="valid" @submit.prevent="save">
            <v-row>
                <v-col cols="12" md="6" lg="6">
                    <v-text-field
                        dense
                        v-model="form.name"
                        class="required"
                        :label="$t('groups.name')"
                        variant="outlined"
                        :rules="validation.name"
                    >
                    </v-text-field>
                </v-col>

                <v-col cols="12">
                    <h3> {{ $t('groups.select_clients') }}</h3>
                    <v-switch 
                v-if="SelectAllFilteredClients.show"
                    v-model="SelectAllFilteredClients.active"
                    :false-value="0"
                    color="blue"
                    :true-value="1"
                    :label="$t('groups.select_all_clients')"
                    ></v-switch>

                    <ClientsSelectTable
                :rows="clients"
                :SelectAllFiltered="SelectAllFilteredClients.active"
                :pagination="pagination"
                :query="query"
                :loading="isLoadingTable"
                :userPermissions="userPermissions"
                :cols="clientCols"
                queryType=""
                :actions="clientActions"
                :class="'clients-select-table'"
                :rowClickHandler="rowClickHandler"
                :selectedRows="form.clients"
                @loadData="loadClients"
            >
            </ClientsSelectTable>
                </v-col>
            </v-row>

            <router-link :to="{ name: 'groups' }">
                <v-btn
                    :class="'float-' + $t('right')"
                    class="colored-btn-cancel"
                >
                    {{ $t("cancel") }}
                </v-btn>
            </router-link>
            <v-btn
                :class="'float-' + $t('right') + ' colored-btn'"
                type="submit"
            >
                <span class="px-2">{{ $t("save") }}</span>
                <img class="crud-icon" src="@/assets/icons/ic_add_2.svg" />
            </v-btn>
        </v-form>
    </v-container>
</template>
<script setup>
import useGroups from "../composables/groups.js";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import ClientsSelectTable from "./includes/clientsSelectTable.vue";

const {
    SelectAllFilteredClients,
    isLoadingTable,
    rowClickHandler,
    loadClients,
    clientCols,
    clientActions,
    clients,
    pagination,
    query,

    isLoading,
    storeItem,
    validation,
    form,
    valid,
    router,
    notify,
    t,
    userPermissions,
} = useGroups();

const save = async () => {
    form.selectAllFilterValue = SelectAllFilteredClients.active ? SelectAllFilteredClients.queryValue : '';
    await storeItem({ ...form }, "groups");
};
</script>
