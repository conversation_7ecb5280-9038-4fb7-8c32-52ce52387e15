<template>

<v-row class="mb-1">
        <v-col>
            <v-btn
            :class="'float-' + $t('left') + ' colored-btn'">
            <download-excel :data="excelData" type="xls">
            <span class="px-2">{{ $t('export_to_excel') }}</span>
            <i class="mdi-microsoft-excel mdi v-icon notranslate v-theme--customTheme v-icon--size-default"></i>
        </download-excel>
        </v-btn>
        </v-col>
    </v-row>

    <data-table :rows="rows" :pagination="cookie.get(`${service.routPath + queryType}`)?.pagination ?? pagination"
    :query="cookie.get(`${service.routPath + queryType}`)?.query ?? query" :loading="loading" hoverable filter
    @rowClicked="rowClickHandler"
    :class="computedClass"
        @loadData="emit('loadData', $event)">
        <template #thead="{sorting, sort}">
            <template v-for="col in cols">
                <table-head v-if="col.sortable" :sortable="col.header" :sort="sort" @sorting="sorting">
                {{ $t(col.field) }}
                </table-head>
                <table-head v-else :sort="sort" @sorting="sorting">
                {{ $t(col.field) }}
                </table-head>
            </template>
            <table-head v-if="showActions">{{ $t("actions") }}</table-head>
        </template>

        <template #tbody="{ row }">
            <table-body v-for="col in cols" v-text="col.cell(row)" :class="{'bg-blue': (selectedRows.includes(row.id) || SelectAllFiltered)}"></table-body>            <table-body v-if="actions">
                <div class="crud-actions-container" v-if="showActions">
                    <div v-for="action in actions">
                        <button
                            v-if="userPermissions.includes(action.perm) && (action.visible ? action.visible(row) : true)"
                            :class="action.class" @click="action.action(row)">
                            <v-icon :icon="action.icon"></v-icon>
                        </button>
                    </div>
                </div>
            </table-body>
        </template>
    </data-table>
</template>
<script setup>
import { defineProps, inject, computed } from "vue";
const service = inject('service');
const t = inject('t');
import cookie from "vue-cookies";

const props = defineProps({
    rowClickHandler: { type: Function, default() { return false; } },
    selectedRows: { type: Array, default() { return []; } },
    rows: { required: true, type: Array, default() { return []; } },
    pagination: { required: true, type: Object, default() { return []; } },
    query: { required: true, type: Object, default() { return []; } },
    queryType: { required: true, type: String, default() { return ''; } },
    loading: { required: true, type: Boolean, default() { return false; } },
    showActions: { type: Boolean, default() { return false; } },
    SelectAllFiltered: { type: Number, default() { return 0; } },
    cols: { required: true, type: Array, default() { return []; } },
    actions: { required: true, type: Array, default() { return []; } },
    userPermissions: { required: true, type: Array, default() { return []; } },
});
const emit = defineEmits(["loadData"]);


const excelData = computed(() => {
                    let excelFields = [];
                    props.rows.forEach(row => {
                        let record = {};
                        props.cols.forEach(item => {
                        record[t(item.field)] = item.cell(row);
                    })
                    excelFields.push(record);
                    })
    
                    return excelFields;
                
                })
            
const computedClass = computed(() => {
    return  props.class;
});

</script>
