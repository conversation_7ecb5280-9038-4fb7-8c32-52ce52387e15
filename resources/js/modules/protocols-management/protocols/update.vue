<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs :path="router.currentRoute.value.path" :title="$t(router.currentRoute.value.meta.breadcrumb)">
        </t-breadcrumbs>
    </v-container>
    <v-container v-if="!isLoading">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("protocols.edit") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>
        <v-form v-model="valid" v-on:submit.prevent="saveProtocol">
            <v-row class="mtn-5">
                <v-col cols="12" md="6" lg="6">
                    <v-text-field v-model="itemData.name" :label="$t('protocols.name')" variant="outlined"
                        :rules="validation.name"></v-text-field>
                </v-col>


                <v-col cols="12" md="6" lg="6">
                    <label>{{ $t('protocols.code') }}</label>
                    <h4>
                        {{ itemData.code }}
                    </h4>
                </v-col>
            </v-row>

            <v-alert :text="`${$t('protocols.form_note')}!`" type="info" class="mb-5"></v-alert>

            <v-row class="mtn-5">
                <v-col cols="12">
                    <v-textarea v-model="itemData.template" :label="$t('protocols.template')" variant="outlined"
                        :rules="validation.template" class="json-template"></v-textarea>
                </v-col>
            </v-row>

            <router-link :to="{ name: 'protocols' }">
                <v-btn :class="'float-' + $t('right')" class="colored-btn-cancel">
                    {{ $t("cancel") }}
                </v-btn>
            </router-link>
            <v-btn :class="'float-' + $t('right') + ' colored-btn'" type="submit">
                <span class="px-2">{{ $t("edit") }}</span>
            </v-btn>
        </v-form>
    </v-container>
</template>

<script setup>
import useProtocols from "../composables/protocols.js";
import { onMounted } from "vue";

import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";

const {
    updateItem,
    validation,
    form,
    valid,
    getItem,
    itemData,
    isLoading,
    router,
} = useProtocols();
const props = defineProps({
    id: {
        required: true,
        type: String,
    },
});
onMounted(() => {
    getItem(props.id, true);
});

const saveProtocol = async () => {
    await updateItem(itemData.value, "protocols", true);
};
</script>
