import { reactive, ref } from 'vue'
import ProtocolsService from "@/services/protocols-service.js";
import useShared from "@/helpers/shared.js";
import protocolTableItems from "../models/protocol-table-items";

export default function useProtocols() {

    const {
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        itemData,
        loadData,
        getItem,
        storeItem,
        updateItem,
        deleteItem,
        parent,
        router,
        errorHandle,
        userPermissions,
        t,
        redirect
    } = useShared()

    service.value = ProtocolsService;

    const {
        cols: protocolCols,
        actions: protocolActions
    } = protocolTableItems(t, deleteItem, redirect);


    const form = reactive({
        'name': null,
        'code': null,
        'file': null,
        'template': null
    });

    const validation = {
        name: [
            validationRules.required,
            validationRules.minLength(3),
            validationRules.maxLength(50),
        ],
        code: [
            validationRules.required,
            validationRules.minLength(2),
            validationRules.maxLength(50),
        ],
        file: [
            validationRules.required
        ],
        template: [
            validationRules.required
        ],
    }

    return {
        itemData,
        tableData,
        pagination,
        query,
        isLoading,
        form,
        validation,
        valid,
        loadData,
        getItem,
        storeItem,
        updateItem,
        deleteItem,
        parent,
        router,
        userPermissions,
        protocolCols,
        protocolActions,
    }
}
