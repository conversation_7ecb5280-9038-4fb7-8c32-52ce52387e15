const ProtocolsIndex = () => import ('./protocols/index.vue');
const ProtocolsCreate = () => import ('./protocols/create.vue');
const ProtocolsUpdate = () => import ('./protocols/update.vue');
const ProtocolsDetail = () => import ('./protocols/details.vue');


const ProtocolsManagementRoutes = [
    {
        path: '/protocols',
        name: 'protocols',
        component: ProtocolsIndex,
        meta: {
            breadcrumb: 'protocols.protocols'
        }
    },
    {
        path: '/protocols/create',
        name: 'protocols/create',
        component: ProtocolsCreate, 
        meta: {
            breadcrumb: 'protocols.add'
        }
    }, 
    {
        path: "/protocols/:id/update",
        name: 'protocols/update',
        component: ProtocolsUpdate,
        props: true,
        meta: {
            breadcrumb: 'protocols.edit'
        }
    },
    {
        path: "/protocols/:id/details",
        name: 'protocols/details',
        component: ProtocolsDetail,
        props: true,
        meta: {
            breadcrumb: 'protocols.details'
        }
    }
];


export default ProtocolsManagementRoutes;
