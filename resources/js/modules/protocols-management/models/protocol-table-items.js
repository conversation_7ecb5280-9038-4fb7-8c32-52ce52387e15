
export default function protocolTableItems(t, deleteItem, redirect) {

    const cols = [
        { header: 'name', field: 'protocols.name', cell: (item) => item.name },
        { header: 'code', field: 'protocols.code', cell: (item) => item.code },
    ];

    const actions = [
        {
            header: 'details',
            perm: 'protocols/details',
            class: 'crud-action-btn',
            icon: "mdi-eye",
            action: (item) => redirect('protocols/details', { id: item.id })
        },
        {
            header: 'update',
            perm: 'protocols/update',
            icon: "mdi-pencil",
            action: (item) => redirect('protocols/update', { id: item.id })
        }
    ];

    return {
        cols,
        actions
    }
}