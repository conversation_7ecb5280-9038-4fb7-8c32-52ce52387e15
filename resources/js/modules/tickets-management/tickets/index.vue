<template>
    <t-modal v-model:show="updateModal" :width="'70%'">
        <update-ticket
            v-model:form="itemData"
            v-model:valid="valid"
            :validation="validation"
            @update="updateModalItem"
            @cancel="cancel()"
        />
    </t-modal>
    <v-container>
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="itemData?.name_ar ?? $t(router.currentRoute.value.meta.breadcrumb)"
            :reset="parent ? false : true"
        >
        </t-breadcrumbs>
    </v-container>
    <v-container>
        <h3>{{ $t('tickets.tickets') }}</h3>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>
        <div class="dt-w-1/2 sm:dt-w-full overflow-hidden">
            <t-data-table
                :rows="tableData"
                :pagination="pagination"
                :query="query"
                :queryType="'LoadData'"
                :loading="isLoading"
                :userPermissions="userPermissions"
                :cols="ticketCols"
                :actions="ticketActions"
                @loadData="loadData"
            >
            </t-data-table>
        </div>
    </v-container>
</template>

<script setup>

import UpdateTicket from "@/modules/tickets-management/tickets/update.vue";
import TDataTable from "@/shared/components/t-data-table.vue";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import TModal from "@/shared/components/t-modal.vue";
import useTickets from "@/modules/tickets-management/composables/tickets";
import {defineProps, onMounted} from "vue";

const {
    parent,
    tableData,
    pagination,
    query,
    userPermissions,
    isLoading,
    updateModal,
    storeModal,
    cancel,
    itemData,
    updateModalItem,
    loadParentData,
    loadData,
    router,
    valid,
    validation,
    form,
    getItem,
    ticketActions,
    ticketCols,
} = useTickets()

const props = defineProps({
    id: {
        required: true,
        type: String
    }
})

onMounted(async () => {
    parent.value = props.id;
    if(parent.value)
        await getItem(parent.value, true);
})
</script>

