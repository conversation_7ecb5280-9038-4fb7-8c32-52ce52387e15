import ticketStatusEnum from '@/enums/ticket-status-enum';

export default function ticketTableItems(t, redirect, showUpdateModal) {
    const {
        cases: ticketStatusCases,
        enumData: ticketStatusEnumData
    } = ticketStatusEnum();

    const cols = [
        { header: 'clients.username.client_id', field: 'tickets.username', cell: (item) => item.user_name },
        { header: 'email', field: 'tickets.email', cell: (item) => item.email },
        { header: 'subject', field: 'tickets.subject', cell: (item) => item.subject },
        {
            header: 'status',
            field: 'tickets.status',
            cell: (item) => {
                if (ticketStatusEnumData[item.status]) {
                    return ticketStatusEnumData[item.status][`title_${t("locale.lang")}`];
                }
                return item.status;
            }
        },
        { header: 'created_at', field: 'tickets.submitted_at', cell: (item) => item.submitted_at },
        { header: 'notes_at', field: 'tickets.notes_at', cell: (item) => item.notes_at },
    ];

    const actions = [
        {
            header: 'details',
            perm: 'tickets/details',
            class: 'crud-action-btn',
            icon: "mdi-eye",
            action: (item) => redirect('tickets/details', { id: item.id }),
        },
        {
            header: 'check',
            perm: 'tickets/handle',
            class: 'crud-action-btn',
            icon: "mdi-check",
            action: (item) => {
                showUpdateModal(item);
            },
            visible: (item) => {
                return item.status === ticketStatusCases.PENDING;
            }
        }
    ];

    return {
        cols,
        actions
    };
}
