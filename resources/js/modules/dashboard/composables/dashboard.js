import {ref} from 'vue';
import useShared from "@/helpers/shared.js";
import DashboardService from '@/services/dashboard-service';
import clientsStatisticService from '@/services/statistics-service';
import { useIntervalFn } from '@vueuse/core'

export default function useDashboard() {
    const {
        errorHandle,
        router,
        service,
        userPermissions,
        form
    } = useShared()

    service.value = DashboardService
    const subscriptions = ref([]);
    const expenses = ref([]);
    const notifications = ref([]);
    const servers = ref([]);
    const clients = ref([])

    const getSubscriptions = async (showLoader = false) => {
        try {
            const response = await service.value.getSubscriptions(showLoader);
            subscriptions.value = response.data;
        } catch (error) {
            await errorHandle(error)
        }
    }

    const getExpenses = async (showLoader = false) => {
        try {
            const response = await service.value.getExpenses(showLoader);
            expenses.value = response.data;
        } catch (error) {
            await errorHandle(error)
        }
    }

    const getNotifications = async (showLoader = false) => {
        try {
            const response = await service.value.getNotifications(showLoader);
            notifications.value = response.data;
        } catch (error) {
            await errorHandle(error)
        }
    }

    const getServers = async (showLoader = false) => {
        try {
            const data = await service.value.getServers(showLoader);
            servers.value = data.data
        } catch (error) {
            await errorHandle(error)
        }
    }

    const getClients = async (showLoader = false) => {
            try {
                const response = await clientsStatisticService.clients(showLoader);
                clients.value = response.data.data;
            } catch (error) {
                errorHandle(error);
            }
        };

    const renderBar = ref(false);


    const chartOptions = ref({
            series: [
                {
                    data: [],
                },
            ],
            chart: {
                type: "bar",
                height: 350,
            },
            plotOptions: {
                bar: {
                    columnWidth: "70%",
                    distributed: true,
                },
            },
            dataLabels: {
                enabled: false,
            },
            stroke: {
                show: true,
                width: 2,
                colors: ["transparent"],
            },
            xaxis: {
                categories: [],
            },
            yaxis: {},
            fill: {
                opacity: 1,
            },
        });

        const subscriptionsStatics = async (showLoader = false) => {
            await getSubscriptions(showLoader);
            chartOptions.value.series = [
                {
                    data: [],
                },
            ];

            let maxTick = 0;

            chartOptions.value.xaxis.categories = [];
            chartOptions.value.series[0].data = [];


            subscriptions.value.package_subscribers_count.map((item) => {
                chartOptions.value.xaxis.categories.push(item["name"]);
                chartOptions.value.series[0].data.push(item["subscriptions_count"]);
                maxTick = Math.max(maxTick, item["subscriptions_count"]);
            });

            chartOptions.value.yaxis.tickAmount = maxTick;
            renderBar.value = true;
        };

        const refreshDashboard = async () => {
            await getServers(true);
            await getClients(true);
            await subscriptionsStatics(true);
            await getExpenses(true);
            await getNotifications(true);
        };

    const { pause, resume, isActive } = useIntervalFn(refreshDashboard, 20000);

    const resetInterval = () => {
        pause();
        resume();
    };

    const reloadData = () => {
        resetInterval();
        refreshDashboard();
    };

    return {
        refreshDashboard,
        reloadData,

        getClients,
        clients,

        getSubscriptions,
        subscriptions,
        subscriptionsStatics,
        chartOptions,
        renderBar,

        getExpenses,
        expenses,

        getNotifications,
        notifications,

        getServers,
        servers
    }
}
