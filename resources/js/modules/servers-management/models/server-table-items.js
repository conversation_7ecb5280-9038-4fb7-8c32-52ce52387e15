
export default function serverTableItems(t, deleteItem, redirect) {

    const cols = [
        { header: 'name', field: 'servers.name', cell: (item) => item.name }, 
        { header: 'ip', field: 'servers.ip', cell: (item) => item.ip }, 
        { header: 'health_status', field: 'servers.health_status', cell: (item) => item.translated_health_status['title_' + t('locale.lang')] }, 
        // { header: 'Provider', field: 'servers.provider', cell: (item) => item.provider }, 
        { header: 'internal_status', field: 'servers.internal_status', cell: (item) => item.translated_internal_status['title_' + t('locale.lang')] }, 
        // { header: 'Country', field: 'servers.country', cell: (item) => item.country }, 
    ];

    const actions = [
        {
            header: 'details',
            perm: 'servers/details',
            class: 'crud-action-btn',
            icon: "mdi-eye",
            action: (item) => redirect('servers/details', { id: item.id })
        },
        {
            header: 'update',
            perm: 'servers/update',
            icon: "mdi-pencil",
            action: (item) => redirect('servers/update', { id: item.id })
        },
        {
            header: 'delete',
            perm: 'servers/delete',
            icon: "mdi-delete",
            action: (item) => deleteItem(item.id)
        }
    ];

    return {
        cols,
        actions
    }
}