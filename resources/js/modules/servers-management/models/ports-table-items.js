import portStatus from "@/enums/port-status-enum.js";

export default function portTableItems(t, deleteItem, redirect) {
    const {
        cases:portStatusCases,
        enumData:portStatusEnumData } = portStatus();

    const cols = [
    { header: "ip", field: "ports.ip", cell: (item) => item.ip },
    { header: "port", field: "ports.port", cell: (item) => item.port },
    {
        header: "status",
        field: "ports.status",
        cell: (item) => {
            if(item.status == portStatusCases.ENABLED)
                return `<span class="rounded bg-success px-4">${portStatusEnumData[item.status][`title_${t("locale.lang")}`]}</span>`;
            return `<span class="rounded bg-red px-4">${portStatusEnumData[item.status][`title_${t("locale.lang")}`]}</span>`;
        },
    },
    { header: "protocols.code.protocol_id", field: "ports.protocol", cell: (item) => item.protocol.code },
    { header: "control_ip_port", field: "ports.control_ip_port", cell: (item) => item.control_ip_port },
    { header: "purpose", field: "ports.purpose", cell: (item) => item.purpose },
    { header: "server_public_key", field: "ports.server_public_key", cell: (item) => item.server_public_key },
    { header: "current_connections_count", field: "ports.current_connections_count", cell: (item) => item.current_connections_count },
    { header: "connections_threshold", field: "ports.connections_threshold", cell: (item) => item.connections_threshold },
    ];

    const actions = [
        {
            header: "update",
            perm: "ports/update",
            icon: "mdi-pencil",
            action: (item) => redirect("ports/update", { id: item.id}),
        },
        {
            header: "delete",
            perm: "ports/delete",
            icon: "mdi-delete",
            action: (item) => deleteItem(item.id),
        },
    ];

    return {
        cols,
        actions,
    };
}
