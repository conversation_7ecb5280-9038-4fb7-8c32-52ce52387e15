import {reactive, ref} from 'vue'
import PortService from "@/services/ports-service.js";
import useShared from "@/helpers/shared.js";
import portTableItems from '../models/ports-table-items';
import portStatus from "@/enums/port-status-enum.js";
import cookie from "vue-cookies";

export default function usePorts( serverId = null ) {

    const {
        notify,
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        itemData,
        getItem,
        parent,
        parentDetails,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        loadParentData,
        cancel,
        loadData,
        saveItem,
        router,
        userPermissions,
        t,
        createConfirmDialog,
        ConfirmDialog,
        errorHandle,
        redirect
    } = useShared()

    service.value = PortService;
    query.value.per_page = 10;


    const deleteItem = async (id, forParent = false,routeName=null) => {
        const dialog = createConfirmDialog(ConfirmDialog)
        dialog.onConfirm(async () => {
            try {
                let response = await service.value.destroy(id);
                notify(response.data.message);
            } catch (error) {
                await errorHandle(error)
            }
            loadPortsData();

        })
        await dialog.reveal();
    }

    const {
        cols: portsCols,
        actions: portsActions
    } = portTableItems(t, deleteItem, redirect);

    const queryTypeState = ref(`Load${serverId}Data`);

    const form = reactive({
        server_id: null,
        parent_id: null,
        protocol: {
            code: '',
            id: null,
        },
        connections_threshold: null,
        current_connections_count: null,
        ip: '',
        port: '',
        status: '',
        control_ip_port: '',
        purpose: '',
        server_public_key: ''
    });

    const validation = {
        protocol: [
            validationRules.required
        ],
        connections_threshold: [
            validationRules.required,
            validationRules.numeric
        ],
        current_connections_count: [
            validationRules.required,
            validationRules.numeric
        ],
        ip: [
            validationRules.required,
            validationRules.ipAddress
        ],
        port: [
            validationRules.required,
            validationRules.numeric,
            validationRules.integer,
            validationRules.minValue(0),
            validationRules.maxValue(65535)
        ],
        status: [
            validationRules.required
        ],
        control_ip_port: [
            validationRules.required,
            validationRules.maxLength(60),
            validationRules.ipPort
        ],
        purpose: [
            validationRules.maxLength(100)
        ],
        server_public_key: [
            validationRules.required
        ]
    }

    const protocols = ref([]);



        const loadPortsData = async (query) => {
            if(parent.value){
                if(query === undefined)
                    query = {
                        search: '',
                        page: 1,
                        sort: '',
                        per_page: 10,
                    }

                try {
                    isLoading.value = true;
                    
                    const {data: {data, meta}} = await service.value.index({
                        parent_id: parent.value,
                        page: query.page,
                        sort: query.sort,
                        size: query.per_page,
                        search: query.search,
                    });
                    tableData.value = data
                    pagination.value = {...pagination.value, page: query.page, total: meta.total, per_page: query.per_page}
                    cookie.set('/ports' + queryTypeState.value, JSON.stringify({pagination: pagination.value, query: query}));
                    isLoading.value = false
                } catch (error) {
                    isLoading.value = false
                    await errorHandle(error)
                }
            }
        }

    const getProtocols = async () => {
        try {
            const response = await service.value.protocols();
            protocols.value = response.data.data;
        } catch (error) {
            await errorHandle(error)
        }
    }


        const storeItem = async (data, route = '', showLoader = false) => {
            if (!valid.value)
                return false;
            try {
                let response = await service.value.store(data, showLoader);
                notify(response.data.message);
                storeModal.value = false;
                await router.push(route);
            } catch (error) {
                await errorHandle(error);
            }
        }


        const updateItem = async (item, route = '', showLoader = false) => {
                if (!valid.value)
                    return false;
                try {
                    let response = await service.value.update(item, item.id, showLoader);
                    notify(response.data.message);
                    updateModal.value = false;
                    await router.push(route);
                } catch (error) {
                    await errorHandle(error);
                }
            }




    return {
        protocols,
        getProtocols,
        loadPortsData,
        queryTypeState,
        portStatus,
        itemData,
        tableData,
        pagination,
        query,
        isLoading,
        loadData,
        validation,
        form,
        valid,
        parent,
        updateModal,
        storeModal,
        showStoreModal,
        showUpdateModal,
        storeModalItem,
        updateModalItem,
        getItem,
        loadParentData,
        storeItem,
        parentDetails,
        updateItem,
        deleteItem,
        saveItem,
        cancel,
        router,
        userPermissions,
        portsCols,
        portsActions,
        form
    }
}