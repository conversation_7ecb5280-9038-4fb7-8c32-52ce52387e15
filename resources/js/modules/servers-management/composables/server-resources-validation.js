
export default function useServerResourcesValidations(t) {
        // Custom validation functions using translations
        const validateRamValue = (value) => {
            if (value === null || value === undefined || value === '')
                return t('servers.ram_required');
    
            const numValue = Number(value);
            if (isNaN(numValue))
                return t('servers.ram_number');
    
            // Common RAM sizes for servers: 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048 GB
            if (numValue < 1)
                return t('servers.ram_min');
    
            if (numValue > 2048)
                return t('servers.ram_max');
    
            // Recommend standard RAM sizes
            const commonCoreCounts = [1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048];
            if (!commonCoreCounts.includes(numValue))
                return t('servers.ram_standard');
    
            return true;
        };
    
        const validateCpuValue = (value) => {
            if (value === null || value === undefined || value === '')
                return t('servers.cpu_required');
    
            const numValue = Number(value);
            if (isNaN(numValue))
                return t('servers.cpu_number');
    
            // Common CPU core counts: 1, 2, 4, 6, 8, 12, 16, 24, 32, 48, 64, 96, 128
            if (numValue < 1)
                return t('servers.cpu_min');
    
            if (numValue > 128)
                return t('servers.cpu_max');
    
            // Common CPU core counts are typically even numbers or multiples of 4/6
            const commonCoreCounts = [1, 2, 4, 6, 8, 12, 16, 24, 32, 48, 64, 96, 128];
            if (!commonCoreCounts.includes(numValue))
                return t('servers.cpu_standard');
    
            return true;
        };
    
        const validateDiskValue = (value) => {
            if (value === null || value === undefined || value === '')
                return t('servers.disk_required');
    
            const numValue = Number(value);
            if (isNaN(numValue))
                return t('servers.disk_number');
    
            // Common disk sizes: 10, 20, 40, 50, 100, 200, 250, 500, 1000, 2000, 4000, 8000 GB
            if (numValue < 10)
                return t('servers.disk_min');
    
            if (numValue > 8000)
                return t('servers.disk_max');
    
            // Common disk sizes are typically multiples of 10 or powers of 2 * 1000
            const commonDiskSizes = [10, 20, 40, 50, 100, 200, 250, 500, 1000, 2000, 4000, 8000];
            if (!commonDiskSizes.includes(numValue))
                return t('servers.disk_standard');
    
            return true;
        };
    
        const validateThresholdValue = (value) => {
            if (value === null || value === undefined || value === '')
                return t('servers.threshold_required');
    
            const numValue = Number(value);
            if (isNaN(numValue))
                return t('servers.threshold_number');
    
            if (numValue < 1)
                return t('servers.threshold_min');
    
            if (numValue > 100)
                return t('servers.threshold_max');
    
            return true;
        };
    
        return {
            validateRamValue,
            validateCpuValue,
            validateDiskValue,
            validateThresholdValue
        };
}