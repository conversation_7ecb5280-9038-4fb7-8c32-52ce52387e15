import { reactive, ref } from 'vue'
import ServersService from "@/services/servers-service.js";
import useShared from "@/helpers/shared.js";
import serverTableItems from "../models/server-table-items";
import providerService from '@/services/provider-management-services/provider-service';
import locationsService from '@/services/locations-service';
import ConfirmDialog from '@/shared/components/confirm-dialog.vue';
import { notify } from '@kyvg/vue3-notification';
import useServerResourcesValidations from './server-resources-validation';
import ServerInternalStatusEnum from '@/enums/server-internal-status-enum';

export default function useServers() {

    const {
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        innerService,
        itemData,
        loadData,
        getItem,
        storeItem,
        updateItem,
        deleteItem,
        parent,
        router,
        errorHandle,
        userPermissions,
        t,
        createConfirmDialog,
        redirect,
        updateModal,
        cancel
    } = useShared()

    service.value = ServersService;
    innerService.value = providerService;

    const providers = ref([]);
    const countries = ref([]);
    const cities = ref([]);

    const {
        cols: serverCols,
        actions: serverActions
    } = serverTableItems(t, deleteItem, redirect);

    const {
        cases: serverInternalStatusCases,
        enumData: serverInternalStatusEnumData
    } = ServerInternalStatusEnum();


    const form = reactive({
        country_id: null,
        location_id: null,
        provider_id: null,
        name: null,
        ip: null,
        socket_port: null,
        cost: null,
        is_free: false,
        internal_status: null,
        ram: null,
        cpu: null,
        disk: null,
        ram_threshold: null,
        cpu_threshold: null,
        disk_threshold: null
    });

    const {
        validateRamValue,
        validateCpuValue,
        validateDiskValue,
        validateThresholdValue
    } = useServerResourcesValidations(t);

    const validation = {
        country_id: [
            validationRules.required
        ],
        location_id: [
            validationRules.required
        ],
        provider_id: [
            validationRules.required
        ],
        name: [
            validationRules.required,
            validationRules.minLength(3),
            validationRules.maxLength(50)
        ],
        ip: [
            validationRules.required,
            validationRules.ipAddress,
            validationRules.maxLength(50)
        ],
        socket_port: [
            validationRules.required,
            validationRules.minValue(0),
            validationRules.maxValue(65535)
        ],
        cost: [
            validationRules.required,
            validationRules.decimal,
            validationRules.minValue(0),
            validationRules.maxValue(9999.99)
        ],
        internal_status: [
            validationRules.required
        ],
        ram: [
            validationRules.required,
        ],
        cpu: [
            validationRules.required,
        ],
        disk: [
            validationRules.required,
        ],
        ram_threshold: [
            validateThresholdValue
        ],
        cpu_threshold: [
            validateThresholdValue
        ],
        disk_threshold: [
            validateThresholdValue
        ]
    }

    const getProviders = async (showLoader = false) => {
        try {
            const { data: { data } } = await service.value.providers(showLoader);
            providers.value = data;
        } catch (error) {
            await errorHandle(error)
        }
    }

    const getLocations = async (parent_id, reset, showLoader = false) => {
        try {
            innerService.value = locationsService;
            const { data: { data } } = await innerService.value.index({
                parent_id: parent_id ? parent_id : '',
                page: 1,
                size: 100000,
                search: '',
            }, showLoader);
            if (!parent_id)
                countries.value = data;
            else cities.value = data;

            if (reset) {
                form.location_id = null;
                itemData.value.location_id = null;
            }
        } catch (error) {
            await errorHandle(error)
        }
    }

    const showUpdateDefaultThresholdsModal = () => {
        updateModal.value = true;
    }

    const updateDefaultThresholds = (updateForm, valid) => {
        if(!valid)
            return;
        form.ram_threshold = updateForm.ram_threshold;
        form.cpu_threshold = updateForm.cpu_threshold;
        form.disk_threshold = updateForm.disk_threshold;
        updateModal.value = false;
        return true;
    }

    const regenerateToken = async (id) => {
        const dialog = createConfirmDialog(ConfirmDialog)
        dialog.onConfirm(async () => {
            try {
                let response = await service.value.regenerateToken(id);
                itemData.value.token = response.data.data;
                notify(response.data.message);
            } catch (error) {
                await errorHandle(error)
            }
        })
        await dialog.reveal();
    }

    return {
        itemData,
        tableData,
        pagination,
        query,
        isLoading,
        form,
        validation,
        valid,
        loadData,
        getItem,
        storeItem,
        updateItem,
        deleteItem,
        parent,
        router,
        userPermissions,
        serverCols,
        serverActions,
        getProviders,
        providers,
        getLocations,
        countries,
        cities,
        updateModal,
        updateDefaultThresholds,
        showUpdateDefaultThresholdsModal,
        cancel,
        t,
        regenerateToken,
        serverInternalStatusEnumData
    }
}
