<template>
    <v-container>
        <v-card width="90%" class="pa-5 mx-auto" color="white" outlined>
            <v-card-title class="headline black" primary-title>
                <h3>
                    {{ $t("servers.change_default_thresholds") }}
                </h3>
            </v-card-title>
            <v-card-text class="pa-5">
                <v-form v-model="validThresholds" v-on:submit.prevent="emit('update', { ...form }, validThresholds)">
                    <v-row class="mt-n2">
                        <v-col cols="12" md="6" lg="4">
                            <v-text-field
                                type="number"
                                v-model="form.ram_threshold"
                                :label="$t('servers.ram_threshold')"
                                variant="outlined"
                                :rules="validation.ram_threshold"
                                suffix="%"
                                :hint="$t('servers.threshold_hint')"
                                persistent-hint
                            >
                            </v-text-field>
                        </v-col>
                        <v-col cols="12" md="6" lg="4">
                            <v-text-field
                                type="number"
                                dense
                                v-model="form.cpu_threshold"
                                :label="$t('servers.cpu_threshold')"
                                variant="outlined"
                                :rules="validation.cpu_threshold"
                                suffix="%"
                                :hint="$t('servers.threshold_hint')"
                                persistent-hint
                            >
                            </v-text-field>
                        </v-col>
                        <v-col cols="12" md="6" lg="4">
                            <v-text-field
                                type="number"
                                dense
                                v-model="form.disk_threshold"
                                :label="$t('servers.disk_threshold')"
                                variant="outlined"
                                :rules="validation.disk_threshold"
                                suffix="%"
                                :hint="$t('servers.threshold_hint')"
                                persistent-hint
                            >
                            </v-text-field>
                        </v-col>
                    </v-row>
                    <v-btn
                        @click="emit('cancel')"
                        :class="'float-' + $t('right')"
                        class="colored-btn-cancel"
                    >
                        {{ $t("cancel") }}
                    </v-btn>
                    <v-btn
                        :class="'float-' + $t('right') + ' colored-btn'"
                        type="submit"
                    >
                        <span class="px-2">{{ $t("edit") }}</span>
                    </v-btn>
                </v-form>
            </v-card-text>
        </v-card>
    </v-container>
</template>
<script setup>
import { ref } from "vue";

const validThresholds = ref(false);
const form = defineModel("form", { type: Object });
const props = defineProps(["validation"]);
const emit = defineEmits(["cancel", "update"]);

</script>
