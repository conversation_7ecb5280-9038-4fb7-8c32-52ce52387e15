const ServersIndex = () => import ('./servers/index.vue');
const ServersCreate = () => import ('./servers/create.vue');
const ServersUpdate = () => import ('./servers/update.vue');
const ServersDetail = () => import ('./servers/details.vue');

const PortsCreate = () => import ('./servers/ports/create.vue');
const PortsUpdate = () => import ('./servers/ports/update.vue');


const ServersManagementRoutes = [
    {
        path: '/servers',
        name: 'servers',
        component: ServersIndex,
        meta: {
            breadcrumb: 'servers.servers'
        }
    },
    {
        path: '/servers/create',
        name: 'servers/create',
        component: ServersCreate, 
        meta: {
            breadcrumb: 'servers.add'
        }
    }, 
    {
        path: "/servers/:id/update",
        name: 'servers/update',
        component: ServersUpdate,
        props: true,
        meta: {
            breadcrumb: 'servers.edit'
        }
    },
    {
        path: "/servers/:id/details",
        name: 'servers/details',
        component: ServersDetail,
        props: true,
        meta: {
            breadcrumb: 'servers.details'
        }
    },

    {
        path: '/ports/create/:server_id/:server_name',
        name: 'ports/create',
        component: PortsCreate, 
        props: true,
        meta: {
            breadcrumb: 'ports.add'
        }
    }, 
    {
        path: "/ports/:id/update",
        name: 'ports/update',
        component: PortsUpdate,
        props: true,
        meta: {
            breadcrumb: 'ports.edit'
        }
    },
];


export default ServersManagementRoutes;
