<template>
    <t-modal v-model:show="updateModal" :width="'70%'">
        <change-default-thresholds
            v-model:form="itemData"
            :validation="validation"
            @update="updateDefaultThresholds"
            @cancel="cancel()"
        />
    </t-modal>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="$t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>
    <v-container v-if="!isLoading">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("servers.edit") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>
        <v-form v-model="valid" v-on:submit.prevent="saveServer">
            <v-row class="mtn-5">
                <v-col cols="12" sm="12">
                    <v-text-field
                        v-model="itemData.name"
                        :label="$t('servers.name')"
                        variant="outlined"
                        :rules="validation.name"
                        class="required"
                    ></v-text-field>
                </v-col>
            </v-row>

            <v-row class="mtn-5">
                <v-col cols="12" sm="6">
                    <v-select
                        v-model="itemData.provider_id"
                        :items="providers"
                        :label="$t('servers.provider')"
                        variant="outlined"
                        :rules="validation.provider_id"
                        class="required"
                        item-title="name"
                        item-value="id"
                    ></v-select>
                </v-col>

                <v-col cols="12" sm="6">
                    <v-text-field
                        type="number"
                        v-model="itemData.ram"
                        step="1"
                        :label="$t('servers.ram')"
                        variant="outlined"
                        :rules="validation.ram"
                        class="required"
                        suffix="GB"
                    ></v-text-field>
                </v-col>
            </v-row>

            <v-row class="mtn-5">
                <v-col cols="12" sm="6">
                    <v-text-field
                        type="number"
                        step="1"
                        v-model="itemData.cpu"
                        :label="$t('servers.cpu')"
                        variant="outlined"
                        :rules="validation.cpu"
                        class="required"
                        suffix="Cores"
                    ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                    <v-text-field
                        type="number"
                        step="1"
                        v-model="itemData.disk"
                        :label="$t('servers.disk')"
                        variant="outlined"
                        :rules="validation.disk"
                        class="required"
                        suffix="GB"
                    ></v-text-field>
                </v-col>
            </v-row>

            <v-row class="mtn-5">
                <v-col cols="12" sm="6">
                    <v-text-field
                        type="number"
                        v-model="itemData.cost"
                        :label="$t('servers.subscription_cost')"
                        variant="outlined"
                        :rules="validation.cost"
                        class="required"
                        suffix="$"
                    ></v-text-field>
                </v-col>

                <v-col cols="12" sm="6">
                    <v-checkbox
                        v-model="itemData.is_free"
                        :label="$t('servers.is_free')"
                        variant="outlined"
                    >
                    </v-checkbox>
                </v-col>
            </v-row>

            <v-row class="mtn-5">
                <v-col cols="12" sm="6">
                    <v-select
                        v-model="itemData.country_id"
                        :items="countries"
                        @update:model-value="getLocations($event, true)"
                        :label="$t('servers.country')"
                        variant="outlined"
                        :rules="validation.country_id"
                        class="required"
                        item-title="name"
                        item-value="id"
                    ></v-select>
                </v-col>

                <v-col cols="12" sm="6">
                    <v-select
                        v-model="itemData.location_id"
                        :items="cities"
                        :label="$t('servers.city')"
                        variant="outlined"
                        :rules="validation.location_id"
                        class="required"
                        item-title="name"
                        item-value="id"
                    ></v-select>
                </v-col>
            </v-row>

            <v-row class="mtn-5">
                <v-col cols="12" sm="6">
                    <v-text-field
                        v-model="itemData.ip"
                        :label="$t('servers.ip')"
                        variant="outlined"
                        :rules="validation.ip"
                        class="required"
                    ></v-text-field>
                </v-col>
                <v-col cols="12" sm="6">
                    <v-text-field
                        v-model="itemData.socket_port"
                        :label="$t('servers.socket_port')"
                        variant="outlined"
                        :rules="validation.socket_port"
                        class="required"
                    ></v-text-field>
                </v-col>
            </v-row>

            <v-row class="mtn-5">
                <v-col cols="12" sm="6">
                    <v-select
                        v-model="itemData.internal_status"
                        :items="Object.values(serverInternalStatusEnumData)"
                        :label="$t('servers.internal_status')"
                        variant="outlined"
                        :rules="validation.internal_status"
                        class="required"
                        :item-title="
                            $t('locale.lang') == 'en' ? 'title_en' : 'title_ar'
                        "
                        item-value="value"
                    ></v-select>
                </v-col>
            </v-row>

            <router-link :to="{ name: 'servers' }">
                <v-btn
                    :class="'float-' + $t('right')"
                    class="colored-btn-cancel"
                >
                    {{ $t("cancel") }}
                </v-btn>
            </router-link>

            <v-btn
                @click="showUpdateDefaultThresholdsModal()"
                :class="'float-' + $t('right') + ' colored-btn'"
            >
                {{ $t("servers.change_default_thresholds") }}
            </v-btn>

            <v-btn
                :class="'float-' + $t('right') + ' colored-btn'"
                type="submit"
            >
                <span class="px-2">{{ $t("edit") }}</span>
            </v-btn>
        </v-form>
    </v-container>
</template>

<script setup>
import useServers from "../composables/servers.js";
import { onMounted } from "vue";

import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import TModal from "@/shared/components/t-modal.vue";
import ChangeDefaultThresholds from "../partials/change-default-thresholds.vue";

const {
    updateItem,
    validation,
    valid,
    getItem,
    itemData,
    isLoading,
    router,
    getProviders,
    providers,
    getLocations,
    countries,
    cities,
    updateModal,
    showUpdateDefaultThresholdsModal,
    updateDefaultThresholds,
    cancel,
    serverInternalStatusEnumData
} = useServers();
const props = defineProps({
    id: {
        required: true,
        type: String,
    },
});
onMounted(async () => {
    await getItem(props.id, true);
    await getProviders(true);
    await getLocations(null, false, true);
    await getLocations(itemData.value.country_id, false, true);
});

const saveServer = async () => {
    await updateItem(itemData.value, "servers", true);
};
</script>
