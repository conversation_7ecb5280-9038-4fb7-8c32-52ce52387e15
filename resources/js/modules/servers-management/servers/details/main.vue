<template>
    <div>
        <v-row class="mt-n3">
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.name") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.name }}
                </b>
            </v-col>
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.ip") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.ip }}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n3">
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.socket_port") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.socket_port }}
                </b>
            </v-col>
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.internal_status") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.translated_internal_status['title_' + $t('locale.lang')] }}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n3">
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.external_status") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.translated_external_status['title_' + $t('locale.lang')] }}
                </b>
            </v-col>
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.health_status") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.translated_health_status['title_' + $t('locale.lang')] }}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n3">
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.country") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.country_name }}
                </b>
            </v-col>
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.city") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.location_name }}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n3">
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.subscription_cost") }} ($)
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.cost }}$
                </b>
            </v-col>
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.is_free") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{
                        itemData.is_free ? $t("yes") : $t("no")
                    }}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n3">
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.ram") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.ram }} GB
                </b>
            </v-col>
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.cpu") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.cpu }} Cores
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n3">
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.disk") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.disk }} GB
                </b>
            </v-col>
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.ram_threshold") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.ram_threshold }}%
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n3">
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.cpu_threshold") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.cpu_threshold }}%
                </b>
            </v-col>
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.disk_threshold") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.disk_threshold }}%
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n3">
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.ram_consumption") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.ram_consumption ?? '-' }}
                </b>
            </v-col>
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.cpu_consumption") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.cpu_consumption ?? '-' }}
                </b>
            </v-col>
        </v-row>

        <v-row class="mt-n3">
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.disk_consumption") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="3">
                <b>
                    {{ itemData.disk_consumption ?? '-' }}
                </b>
            </v-col>
        </v-row>
        <v-row class="mt-n3">
            <v-col cols="12" sm="3">
                <v-label>
                    {{ $t("servers.token") }}
                </v-label>
            </v-col>
            <v-col cols="12" sm="6">
                <b>
                    <bdi>{{ itemData.token ?? '-' }}</bdi>
                </b>
            </v-col>
            <v-col cols="12" sm="3">
                <v-btn
                    @click="regenerateToken(itemData.id)"
                    :class="'float-' + $t('right') + ' colored-btn'">
                    <span class="px-2">{{ $t('servers.regenerate_token') }}</span>
                    <img class="crud-icon" src="@/assets/icons/ic_arrow_rotate_right.svg">
                </v-btn>
            </v-col>
        </v-row>
    </div>
</template>

<script setup>
import { onMounted } from 'vue';

const props = defineProps({
    itemData: {
        required: true,
        type: Object
    },
    regenerateToken: {
        required: true,
        type: Function
    }
})

onMounted(() => {
})
</script>
