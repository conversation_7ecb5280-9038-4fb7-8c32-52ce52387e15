<template>
    <v-row class="mt-4">
        <v-col>
            <v-tabs
                v-model="tabs"
                bg-color="primary"
            >
                <v-tab value="ports">{{ $t('ports.ports') }}</v-tab>
                <v-tab value="resources">{{ $t('servers.resources') }}</v-tab>
            </v-tabs>

            <v-card-text>
                <v-tabs-window v-model="tabs">
                    <v-tabs-window-item value="ports">
                        <ports-tab :itemData="props.itemData" :id="props.id" />
                    </v-tabs-window-item>

                    <v-tabs-window-item value="resources">
                        <resources-tab :itemData="props.itemData" :id="props.id" />
                    </v-tabs-window-item>
                </v-tabs-window>
            </v-card-text>
        </v-col>
    </v-row>
</template>

<script setup>
import { ref } from 'vue';
import PortsTab from './tabs/ports.vue';
import ResourcesTab from './tabs/resources.vue';

const props = defineProps({
    itemData: {
        required: true,
        type: Object
    },
    id: {
        required: true,
        type: String
    }
});

// Local state for tabs
const tabs = ref('ports');
</script>
