
export default function locationTableItems(t, redirect, showUpdateModal, deleteItem,getAutoLocationLabel,getAutoProtocolLabel) {

    const cols = [
        { header: 'name', field: 'name', cell: (item) => item.name },
        { header: 'code', field: 'code', cell: (item) => item.code },
        {header: 'locations.name.auto_location_id', field: 'locations.auto_location',cell: (item) => getAutoLocationLabel(item.auto_location_id)},
        {header: 'protocols.name.auto_protocol_id', field: 'locations.auto_protocol',cell: (item) => getAutoProtocolLabel(item.auto_protocol_id)},
    ];

    const actions = [
        {
            header: 'details',
            perm: 'locations/details',
            class: 'crud-action-btn',
            icon: "mdi-eye",
            action: (item) => redirect('locations', { id: item.id })
        },
        {
            header: 'update',
            perm: 'locations/update',
            icon: "mdi-pencil",
            action: (item) => showUpdateModal(item)
        },
        {
            header: 'delete',
            perm: 'locations/delete',
            icon: "mdi-delete",
            action: (item) => deleteItem(item.id, true)
        }
    ];

    return {
        cols,
        actions
    }
}
