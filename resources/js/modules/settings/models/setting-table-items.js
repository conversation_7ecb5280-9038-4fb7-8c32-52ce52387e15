
export default function settingTableItems(t, redirect, showUpdateModal) {

    const cols = [
        { header: 'name', field: 'name', cell: (item) => item.name },
        { header: 'type', field: 'settings.type', cell: (item) => item.type },
        {
            sortable: false,
            header: 'value',
            field: 'settings.value',
            cell: (item) => item.type === 'bool' ? t('boolean_type.'+ item.value) : item.value
        }
    ];

    const actions = [
        {
            header: 'update',
            perm: 'settings/update',
            icon: "mdi-pencil",
            action: (item) => showUpdateModal(item)
        },
    ];

    return {
        cols,
        actions
    }
}
