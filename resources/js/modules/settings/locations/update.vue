<template>
  <v-container>
    <v-card width="90%" class="pa-5 mx-auto" color="white" outlined>
      <v-card-title class="headline black" primary-title>
        <h3>
          {{ $t('locations.edit') }}
        </h3>
      </v-card-title>
      <v-card-text class="pa-5">
            <v-form
                v-model="valid"
                v-on:submit.prevent="emit('update', {...updateForm}, 'locations', true)"
            >
          <v-row class="mt-n2">
            <v-col cols="12" md="6" lg="6">
              <v-text-field
                v-model="updateForm.name"
                :label="$t('name')"
                variant="outlined"
                :rules="validation.name"
                class="required"
              >
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6" lg="6">
              <v-text-field
                v-model="updateForm.code"
                :label="$t('locations.code')"
                variant="outlined"
                :rules="validation.code"
              >
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6" lg="6">
              <v-text-field
                v-model="updateForm.free_servers_count"
                :label="$t('locations.free_servers_count')"
                variant="outlined"
                :rules="validation.free_servers_count"
              >
              </v-text-field>
            </v-col>
            <v-col cols="12" md="6" lg="6">
              <v-text-field
                v-model="updateForm.premium_servers_count"
                :label="$t('locations.premium_servers_count')"
                variant="outlined"
                :rules="validation.premium_servers_count"
              >
              </v-text-field>
            </v-col>
          </v-row>
              <v-row class="mt-n2">
              <v-col cols="12" md="6" lg="6">
                  <v-select
                      :label="$t('locations.auto_location')"
                      v-model="updateForm.auto_location_id"
                      :items="autoLocations"
                      :item-title="'name'"
                      item-value="id"
                      variant="solo"
                      clearable
                  >
                  </v-select>
              </v-col>
                  <v-col cols="12" md="6" lg="6">
                      <v-select
                          :label="$t('locations.auto_protocol')"
                          v-model="updateForm.auto_protocol_id"
                          :items="autoProtocols"
                          :item-title="'code'"
                          item-value="id"
                          variant="solo"
                          clearable
                      >
                      </v-select>
                  </v-col>
          </v-row>
          <v-btn
            @click="emit('cancel')"
            :class="'float-' + $t('right')"
            class="colored-btn-cancel"
          >
            {{ $t("cancel") }}
          </v-btn>
                <v-btn
                    :class="'float-' + $t('right') + ' colored-btn'"
                    type="submit"
                >
                    <span class="px-2">{{ $t("edit") }}</span>
                </v-btn>
        </v-form>
      </v-card-text>
    </v-card>
  </v-container>
</template>
<script setup>
import {ref, defineProps, onMounted} from 'vue';
import useLocations from "@/modules/settings/composables/locations";


const {
    autoLocations,
    getAutoLocations,
    getAutoProtocols,
    autoProtocols,
} = useLocations()


const valid = defineModel("valid", {type: Boolean});
const form = defineModel("form", {type: Object});
const props = defineProps(["validation"]);
const emit = defineEmits(["cancel", "update"]);

const updateForm = ref({...form.value});

onMounted( () => {
    getAutoLocations();
    getAutoProtocols();
});

</script>

