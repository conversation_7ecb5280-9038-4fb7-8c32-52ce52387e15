const SettingsIndex = () => import("@/modules/settings/settings/index.vue");
const ContentIndex = () => import("@/modules/settings/settings/index-content.vue");
const SettingsUpdate = () => import("@/modules/settings/settings/update.vue");
const SettingsDetail = () => import("@/modules/settings/settings/details.vue");
const ContentUpdate = () => import("@/modules/settings/settings/update-content.vue");

const SettingsRoutes = [
    {
        path: '/settings/:id?',
        name: 'settings',
        component: SettingsIndex,
        props: true,
        meta: {
            breadcrumb: 'settings.settings'
        }
    },
    {
        path: '/index-content',
        name: 'settings/indexContent',
        component: ContentIndex,
        props: true,
        meta: {
            breadcrumb: 'settings.contents'
        }
    },
    {
        path: "/settings/:id/update",
        name: 'settings/update',
        component: SettingsUpdate,
        props: true
    },
    {
        path: "/settings/:id/update-content",
        name: 'settings/update-content',
        component: ContentUpdate,
        props: true
    },
    {
        path: "/settings/:id/details",
        name: 'settings/details',
        component: SettingsDetail,
        props: true,
        meta: {
            breadcrumb: 'settings.details'
        }
    }
];

export default SettingsRoutes;
