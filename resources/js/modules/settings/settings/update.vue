<template>
    <v-container>
        <v-card width="90%" class="pa-5 mx-auto" color="white" outlined>
            <v-card-title class="headline black" primary-title>
                <h3>
                    {{ $t('settings.edit') }}
                </h3>
            </v-card-title>
            <v-card-text class="pa-5">
                <v-form
                    v-model="valid"
                    v-on:submit.prevent="emit('update', {...updateForm}, 'settings', true)"
                >
                    <v-row class="mt-n2">
                        <v-col>
                            <v-label>{{ $t("settings.name") + " : " + updateForm.name }}</v-label>
                        </v-col>
                        <v-col>
                            <v-text-field
                                v-if="updateForm.type === 'text' || updateForm.type === 'email' || updateForm.type === 'link'"
                                v-model="updateForm.value"
                                :label="$t('settings.value')"
                                variant="outlined"
                                :type="updateForm.type === 'email' ? 'email' : 'text'"
                            />

                            <v-text-field
                                v-else-if="updateForm.type === 'numeric'"
                                v-model="updateForm.value"
                                :label="$t('settings.value')"
                                variant="outlined"
                                type="number"
                            />

                            <v-checkbox
                                v-else-if="updateForm.type === 'bool'"
                                v-model="updateForm.value"
                                false-value="0"
                                true-value="1"
                                :label="$t('settings.value')"
                            />

                        </v-col>
                    </v-row>
                    <v-btn
                        @click="emit('cancel')"
                        :class="'float-' + $t('right')"
                        class="colored-btn-cancel"
                    >
                        {{ $t("cancel") }}
                    </v-btn>
                    <v-btn
                        :class="'float-' + $t('right') + ' colored-btn'"
                        type="submit"
                    >
                        <span class="px-2">{{ $t("edit") }}</span>
                    </v-btn>
                </v-form>
            </v-card-text>
        </v-card>
    </v-container>
</template>
<script setup>
import { ref } from 'vue';


const emit = defineEmits(["cancel", "update"]);
const valid = defineModel("valid", {type: Boolean});
const form = defineModel("form", {type: Object});
const props = defineProps(["validation"]);
const updateForm = ref({...form.value});


</script>

