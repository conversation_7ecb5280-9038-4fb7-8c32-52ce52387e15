<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="
                itemData.name ?? $t(router.currentRoute.value.meta.breadcrumb)
            "
        >
        </t-breadcrumbs>
    </v-container>
    <v-container v-if="itemData" class="py-4">
        <v-row>
            <v-col cols="12" class="font-weight-bold text-h5">
                {{ $t('settings.contents') }}
            </v-col>
        </v-row>
        <v-divider class="my-4"></v-divider>

        <v-row class="mt-3">
            <v-col cols="12" md="4">
                <v-label class="text-subtitle-1 font-weight-medium">
                    {{ $t("settings.name") }}
                </v-label>
            </v-col>
            <v-col cols="12" md="8">

                    <b>{{ itemData.name }}</b>

            </v-col>
        </v-row>

        <v-row class="mt-3">
            <v-col cols="12" md="4">
                <v-label class="text-subtitle-1 font-weight-medium">
                    {{ $t("settings.type") }}
                </v-label>
            </v-col>
            <v-col cols="12" md="8">

                    <b>{{ itemData.type }}</b>
             
            </v-col>
        </v-row>


        <v-row class="mt-3">
            <v-col cols="12" md="4">
                <v-label class="text-subtitle-1 font-weight-medium">
                    {{ $t("settings.value") }}
                </v-label>
            </v-col>
            <v-col cols="12" md="8">
                <div class="quill-content-view">

                    <div v-html="itemData.value"></div>
                </div>
            </v-col>
        </v-row>

    </v-container>
</template>

<script setup>
import { onMounted } from "vue";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import useSettings from "@/modules/settings/composables/settings";

const { parent,getItem, itemData, isLoading, router } = useSettings();

const props = defineProps({
    id: {
        required: true,
        type: String
    }
});

onMounted(() => {
    parent.value = props.id
    getItem(props.id);
});
</script>

<style scoped>
.quill-content-view {
    background-color: #f4f4f9;
    overflow-y: scroll;
    max-height: 220px;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    white-space: pre-wrap;
    font-size: 1rem;
    line-height: 1.5;
    color: #333;
}

@media (min-height: 700px) {
    .quill-content-view {
        max-height: 365px;
    }
}
</style>
