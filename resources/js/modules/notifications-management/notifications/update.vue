<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="$t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>
    <v-container v-if="!isLoading">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t('notifications.edit') }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>
        <v-form v-model="valid" v-on:submit.prevent="saveNotification">
            <v-row class="mt-n5">
                <v-col>
                    <v-text-field
                        v-model="itemData.subject"
                        :label="$t('notifications.subject')"
                        variant="solo"
                        :rules="validation.subject"
                        class="required"
                    >
                    </v-text-field>
                </v-col>
            </v-row>
            <v-row class="mt-n5">
                <v-col>
                    <v-textarea
                        dense
                        v-model="itemData.content"
                        :label="$t('notifications.content')"
                        variant="solo"
                        :rules="validation.content"
                    >
                    </v-textarea>
                </v-col>
            </v-row>

            <v-row class="mt-n5">
                <v-col>
                    <v-select
                        :label="$t('notifications.type')"
                        v-model="itemData.type"
                        :items="notificationsTypes"
                        :item-title=" 'value'"
                        item-value="value"
                        variant="solo"
                        clearable
                    >
                    </v-select>
                </v-col>
            </v-row>

            <v-row class="mt-n5" v-if="itemData.type === 'group'">
                <v-col>
                    <v-select
                        :label="$t('notifications.group')"
                        v-model="itemData.group_id"
                        :items="groups"
                        :item-title="'name'"
                        item-value="id"
                        variant="solo"
                        clearable
                    >
                    </v-select>
                </v-col>
            </v-row>

            <v-row class="mt-n5">
                <v-col>
                    <v-text-field
                        v-model="itemData.scheduled_at"
                        :type="'datetime-local'"
                        :label="$t('notifications.scheduled_at')"
                        variant="solo"
                    >
                    </v-text-field>
                </v-col>
            </v-row>

            <router-link :to="{ name: 'notifications'}">
                <v-btn :class=" 'float-'+$t('right') " class="colored-btn-cancel">
                    {{ $t('cancel') }}
                </v-btn>
            </router-link>
            <v-btn :class="'float-'+$t('right') + ' colored-btn'"
                   type="submit"
            >
                <span class="px-2">{{ $t('edit') }}</span>
            </v-btn>
        </v-form>

    </v-container>
</template>
<script setup>
import {onMounted} from 'vue';
import useNotifications from "@/modules/notifications-management/composables/notifications";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";


const {
    getNotificationsTypes,
    notificationsTypes,
    router,
    isLoading,
    form,
    itemData,
    validation,
    valid,
    groups,
    storeItem,
    getItem,
    getGroups,
    updateItem,
} = useNotifications()

const props = defineProps({
    id: {
        required: true,
        type: String
    }
})

onMounted( () => {
    getNotificationsTypes();
    getGroups();
    getItem(props.id, true)
});


const saveNotification = async () => {
    await updateItem(itemData.value, 'notifications', true)
}

</script>

