import { reactive, ref } from 'vue'
import NotificationService from "@/services/notification-management-services/notification-service.js";
import useShared from "@/helpers/shared.js";
import notificationTableItems from "../models/notification-table-items.js";
import clientNotificationsService from "@/services/notification-management-services/client-notification-service.js";
import cookie from "vue-cookies";
import notificationTypeEnum from "@/enums/notification-type-enum";
import notificationStatusEnum from "@/enums/notification-status-enum";

export default function useNotifications() {

    const {
        validationRules,
        tableData,
        pagination,
        valid,
        query,
        isLoading,
        service,
        itemData,
        loadData,
        getItem,
        storeItem,
        updateItem,
        deleteItem,
        parent,
        router,
        errorHandle,
        userPermissions,
        t,
        cookie,
        redirect
    } = useShared()

    service.value = NotificationService;

    const groups = ref([]);
    const notificationsTypes = ref([]);
    const clientNotifications = ref([]);

    const {
        cases: notificationTypeCases,
        enumData: notificationTypeEnumData
    } = notificationTypeEnum();

    const {
        cases: notificationStatusCases,
        enumData: notificationStatusEnumData
    } = notificationStatusEnum();


    const getGroups = async () => {
        try {
            const response = await service.value.getGroups();
            groups.value = response.data.data;
        } catch (error) {
            await errorHandle(error);
        }
    };

    const getNotificationsTypes = () => {
        // Convert the enum to the format expected by the select components
        notificationsTypes.value = Object.keys(notificationTypeEnumData).map(key => ({
            value: key,
            title_en: notificationTypeEnumData[key].title_en,
            title_ar: notificationTypeEnumData[key].title_ar
        }));
    };

    const getGroupLabel = (id) => {
        const group = groups.value.find(ft => ft.id === id);
        return  group?group.name:'';
    };


    const {
        cols: notificationCols,
        actions: notificationActions
    } = notificationTableItems(t,redirect,deleteItem);


    const form = reactive({
        'subject': null,
        'content': null,
        'type': null,
        'group_id': null,
        'client_id': null,
        'scheduled_at':null
    });

    const clientNotificationsQuery = ref({
        search: '',
        page: 1,
        sort:'',
        per_page: 10,
    })


    const clientNotificationsPagination = ref({})
    const clientNotificationsIsLoading = ref(true)

    const getClientNotifications = async (clientNotificationsQuery) => {
        try {
            if (itemData.value) {
                clientNotificationsIsLoading.value = true;

                const {data: {data, meta}} = await clientNotificationsService.index({
                    notification_id: itemData.value.id,
                    page: clientNotificationsQuery.page,
                    size: clientNotificationsQuery.per_page,
                    search: clientNotificationsQuery.search,
                });
                clientNotifications.value = data

                clientNotificationsPagination.value = {...clientNotificationsPagination.value, page: clientNotificationsQuery.page,
                     total: meta.total, per_page: clientNotificationsQuery.per_page}

                cookie.set(`${service.value.routPath}Load${itemData.value.id}clientNotificationsData`, 
                    JSON.stringify({pagination: clientNotificationsPagination.value, query: clientNotificationsQuery}));

                clientNotificationsIsLoading.value = false
            }
        } catch (error) {
            clientNotificationsIsLoading.value = false
            await errorHandle(error)
        }
    }


    const validation = {
        subject: [
            validationRules.required
        ],
        content: [
            validationRules.required
        ],
        type: [
            validationRules.required
        ],
        group_id: [
            validationRules.required
        ],
        client_id: [
            validationRules.required
        ],
        scheduled_at: [
            validationRules.required
        ],
    }

    const tabs = ref('notifications_clients')

    return {
        itemData,
        tableData,
        pagination,
        query,
        isLoading,
        form,
        validation,
        valid,
        loadData,
        getItem,
        storeItem,
        updateItem,
        deleteItem,
        parent,
        router,
        userPermissions,
        notificationCols,
        notificationActions,
        getGroups,
        groups,
        getNotificationsTypes,
        notificationsTypes,
        getClientNotifications,
        clientNotifications,
        getGroupLabel,
        tabs,
        t,
        redirect,
        clientNotificationsQuery,
        clientNotificationsIsLoading,
        clientNotificationsPagination,
        notificationTypeCases,
        notificationTypeEnumData,
        notificationStatusCases,
        notificationStatusEnumData
    }
}
