import notificationTypeEnum from '@/enums/notification-type-enum';
import notificationStatusEnum from '@/enums/notification-status-enum';

export default function notificationTableItems(t, redirect, deleteItem) {
    const {
        cases: notificationTypeCases,
        enumData: notificationTypeEnumData
    } = notificationTypeEnum();

    const {
        cases: notificationStatusCases,
        enumData: notificationStatusEnumData
    } = notificationStatusEnum();

    const cols = [
        { header: 'subject', field: 'notifications.subject', cell: (item) => item.subject },
        { header: 'content', field: 'notifications.content', cell: (item) => item.content },
        {
            header: 'type',
            field: 'notifications.type',
            cell: (item) =>  notificationTypeEnumData[item.type][`title_${t("locale.lang")}`]

        },
        { header: 'groups.name.group_id', field: 'notifications.group', cell: (item) =>  item.group_id ? item.group.name : '-'},
        {
            header: 'Status',
            field: 'notifications.status',
            cell: (item) => notificationStatusEnumData[item.status][`title_${t("locale.lang")}`]

        },
        { header: 'scheduled_at', field: 'notifications.scheduled_at', cell: (item) => item.scheduled_at },
    ];

    const actions = [
        {
            header: 'details',
            perm: 'notifications/details',
            class: 'crud-action-btn',
            icon: "mdi-eye",
            action: (item) => redirect('notifications/details', { id: item.id })
        },
        {
            header: 'update',
            perm: 'notifications/update',
            icon: "mdi-pencil",
            action: (item) => redirect('notifications/update', { id: item.id }),
            visible: (item) => {
                return item.status === notificationStatusCases.SCHEDULED && (new Date() < new Date(item.scheduled_at))
            }
        },
        {
            header: 'delete',
            perm: 'notifications/delete',
            icon: "mdi-delete",
            action: (item) => deleteItem(item.id),
            visible: (item) => {
                return item.status === notificationStatusCases.SCHEDULED && new Date() < new Date(item.scheduled_at);
            }
        }
    ];

    return {
        cols,
        actions
    }
}
