import clientNotificationStatusEnum from '@/enums/client-notification-status-enum';
import clientTypesEnum from '@/enums/client-types-enum';

export default function NotificationsClientTableItems(t, redirect, deleteItem) {
    const {
        cases: clientNotificationStatusCases,
        enumData: clientNotificationStatusEnumData
    } = clientNotificationStatusEnum();

    const {
                cases: clientTypesCases,
                enumData: clientTypesEnumData
            } = clientTypesEnum();

    const cols = [
        { header: 'user_name', field: 'clients.username', cell: (item) => item.client },
        { header: 'email', field: 'clients.email', cell: (item) => item.email },
        { header: 'type', field: 'clients.type', cell: (item) => clientTypesEnumData[item.type][`title_${t("locale.lang")}`] },
        { header: 'sent_date', field: 'notifications.sent_date', cell: (item) => item.sent_at },
        {
            header: 'status',
            field: 'notifications.status',
            cell: (item) => {
                if (clientNotificationStatusEnumData[item.status]) {
                    return clientNotificationStatusEnumData[item.status][`title_${t("locale.lang")}`];
                }
                return item.status;
            }
        },
    ];

    const actions = [
    ];

    return {
        cols,
        actions
    }
}
