
export default function expenseTableItems(t, deleteItem, redirect) {

    const cols = [
        { header: 'servers.name.server_id', field: 'expenses.server', cell: (item) => item.server?.name },
        { header: 'description', field: 'expenses.description', cell: (item) => item.description },
        { header: 'amount', field: 'expenses.amount', cell: (item) => item.amount },
        { header: 'payment_date', field: 'expenses.payment_date', cell: (item) => item.payment_date },

    ];

    const actions = [
        {
            header: 'details',
            perm: 'expenses/details',
            class: 'crud-action-btn',
            icon: "mdi-eye",
            action: (item) => redirect('expenses/details', { id: item.id })
        },
        {
            header: 'update',
            perm: 'expenses/update',
            icon: "mdi-pencil",
            action: (item) => redirect('expenses/update', { id: item.id })
        },
        {
            header: 'delete',
            perm: 'expenses/delete',
            icon: "mdi-delete",
            action: (item) => deleteItem(item.id)
        }
    ];


    return {
        cols,
        actions
    }
}