<template>
    <v-container v-if="!isLoading">
        <t-breadcrumbs
            :path="router.currentRoute.value.path"
            :title="itemData.name ?? $t(router.currentRoute.value.meta.breadcrumb)"
        >
        </t-breadcrumbs>
    </v-container>

    <v-container v-if="!isLoading" class="font-weight-bold">
        <v-row class="p-0 m-0">
            <v-col cols="12" class="font-weight-black">
                {{ $t("expenses.details") }}
            </v-col>
        </v-row>
        <v-divider :thickness="2" class="mt-3 mb-5"></v-divider>

        <v-row>
            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("expenses.server") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ itemData.server?.name }}
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("expenses.amount") }} ($)
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ itemData.amount }}$
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("expenses.description") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ itemData.description }}
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("expenses.type") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ expenseTypes().enumData[itemData.type][`title_${$t('locale.lang')}`] }}
                </b>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <v-label>
                    {{ $t("expenses.payment_date") }}
                </v-label>
            </v-col>

            <v-col sm="12" md="6" class="py-1">
                <b>
                    {{ itemData.payment_date }}
                </b>
            </v-col>

            </v-row>


    </v-container>
</template>
<script setup>
import useExpenses from "../composables/expenses.js";
import TBreadcrumbs from "@/shared/components/t-breadcrumbs.vue";
import {onMounted} from "vue";

const {
    isLoading,
    expenseTypes,
    getItem,
    validation,
    itemData, valid,
    router,
} = useExpenses();

const props = defineProps({
    id: {
        required: true,
        type: String
    }
})

onMounted(() => {
    getItem(props.id, true)
})

</script>
