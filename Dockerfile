# Build Vite Assets stage-1
FROM docker.arvancloud.ir/node:18 as vite
RUN mkdir -p /var/www/laravel/public

# Container Work Directory
WORKDIR /var/www/laravel

# Install dependencies
COPY package*.json ./
RUN npm ci

# Build App
# App will build in folder name "build" inside public dir (by default)
COPY vite.config.js ./
COPY resources/ resources/
RUN npm run build


# Build final image stage-2
#In this image there is Nginx web-server will read from /public 
FROM tatweer:php82-alpine as publish

#Install dependencies
COPY --chown=php:nginx artisan .
COPY --chown=php:nginx composer.* ./
RUN composer install --no-dev --no-interaction --no-autoloader --no-scripts

# Copy project files
COPY --chown=php:nginx / .
COPY --chown=php:nginx --from=vite /var/www/laravel/public/build/ ./public/build/

# run.scripts contains (migrate and optomizing commands)
COPY .run-script/run.scripts /etc/cont-init.d/scripts

# regenerates the autoload_classmap.php file.
RUN composer dump-autoload --optimize

# creates a symbolic link from public/storage to storage/app/public, making the files stored in the storage/app/public directory accessible from the web.
RUN php artisan storage:link

RUN chown -R php:nginx /www
