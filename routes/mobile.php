<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MobileControllers\ProtocolController;
use App\Http\Controllers\MobileControllers\Auth\AuthenticationController;
use App\Http\Controllers\MobileControllers\Auth\SocialLoginController;
use App\Http\Controllers\MobileControllers\PortController;
use App\Http\Controllers\MobileControllers\PackageController;
use App\Http\Controllers\MobileControllers\ServerController;
use App\Http\Controllers\MobileControllers\SettingController;
use App\Http\Controllers\MobileControllers\TicketController;
use Illuminate\Http\Request;

/* ==================== Auth ====================*/

Route::post('login', [AuthenticationController::class, 'login']);
Route::post('register', [AuthenticationController::class, 'register']);
Route::post('resend-verification-code', [AuthenticationController::class, 'resendVerificationCode']);
Route::post('verify-email', [AuthenticationController::class, 'verifyEmail']);
Route::post('send-reset-password-code', [AuthenticationController::class, 'sendResetPasswordCode']);
Route::post('verify-reset-code-login', [AuthenticationController::class, 'validateResetCodeAndLogin']);
Route::post('social-login', [SocialLoginController::class, 'register']);

/* ==================== Settings ====================*/
Route::get('/settings', [SettingController::class, 'index']);
Route::get('/settings/{code}', [SettingController::class, 'show']);

Route::middleware('auth:client')->group(function () {
    /* ==================== Auth ====================*/
    Route::post('logout', [AuthenticationController::class, 'logout']);
    Route::post('reset-password', [AuthenticationController::class, 'resetPassword']);
    Route::post('delete-my-account', [AuthenticationController::class, 'deleteMyAccount']);
    Route::post('change-my-password', [AuthenticationController::class, 'changeMyPassword']);
    Route::post('detect-country', [AuthenticationController::class, 'detectCountry']);

    /* ==================== Protocol ====================*/
    Route::get('/protocols', [ProtocolController::class, 'index']);
    Route::get('/protocols/{code}', [ProtocolController::class, 'show']);

    /* ==================== Server ====================*/
    Route::get('/countries', [ServerController::class, 'countries']);
    Route::get('/location-protocols/{location}', [ServerController::class, 'protocols']);

    /* ==================== Port ====================*/
    Route::post('/servers-to-connect', [PortController::class, 'serversToConnect']);
    
    /* ==================== Package ====================*/
    Route::get('/packages', [PackageController::class, 'packages']);
    Route::post('/package-subscribe', [PackageController::class, 'subscribe']);


});


/* ==================== Feedback ====================*/
Route::post('ticket', [TicketController::class, 'store']);


Route::middleware('auth:client')->get('client', function (Request $request) {
    return $request->user();
});
